﻿ <%@ WebService Language="C#" Class="WSZXDDataPull" %>

using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Web;
using System.Web.Services;
using nrWebClass;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using System.Collections.Generic;
using System.Data.SqlClient;


[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
//若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消对下行的注释。 12
// [System.Web.Script.Services.ScriptService]
public class WSZXDDataPull : System.Web.Services.WebService
{
    private string connStr = "";
    public WSZXDDataPull()
    {
        connStr = clsConfig.GetConfigValue("OAConnStr");
        
    }

    /// <summary>
    /// 获取物料卡id122
    /// </summary>
    /// <param name="tzid">套账id</param>
    /// <param name="wlkbh">物料卡编号</param>
    [WebMethod(Description = "保存物料卡信息")]
    public string GetWlkInfo(string tzid, string wlkbh)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @"
                    SELECT a.id,a.wlkbh,a.dhdbh,b.tm,b.sl,b.xh,a.chdm,c.dw,c.chmc
                    FROM cl_t_wlkb a
                    INNER JOIN dbo.CL_T_chdmb c with(nolock) ON a.chdm = c.chdm 
                    LEFT JOIN cl_t_wlkmxb b on a.id=b.id 
                    WHERE a.wlkbh='{0}'   
                    ORDER BY b.xh DESC      
                ";

                str_sql = string.Format(str_sql, wlkbh);

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "cl_t_wlkmxb";
                        rtMsg = SerializeDataTableXml(dt);
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + str_sql);
                }
            }
        }

        return rtMsg;
    }
    
    [WebMethod(Description = "保存物料卡信息2转化为字符串")]
    public string SaveWlkInfo2String(string infoStr, string username, string dhtzdbh, string tzid, string id, string chdm)
    {

        // 将JSON字符串转换为DJInfo对象
        JsonSerializerSettings setting = new JsonSerializerSettings();
        setting.NullValueHandling = NullValueHandling.Ignore;
        List<DJInfo2String> djInfoList = JsonConvert.DeserializeObject<List<DJInfo2String>>(infoStr,setting);
        DataTable cwInfo = new DataTable();
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-tmValue:123");
        // 添加列
        cwInfo.Columns.Add("tm", typeof(string));
        cwInfo.Columns.Add("sl", typeof(string));
        cwInfo.Columns.Add("chdm", typeof(string));

        // 添加数据行
        foreach(DJInfo2String dr in djInfoList)
        {
            //新建行的赋值
            DataRow dtr = cwInfo.NewRow();//创建新行
            dtr["tm"] = dr.tm;
            dtr["sl"] = dr.sl;
            dtr["chdm"] = dr.chdm;
            cwInfo.Rows.Add(dtr);
        }
        return SaveWlkInfo(cwInfo,username,dhtzdbh,tzid,id,chdm);
    }

    /// <summary>
    /// 保存物料卡信息
    /// </summary>
    /// <param name="info">物料卡信息</param>
    /// <param name="username">当前用户名</param>
    /// <param name="dhtzdbh">到货通知单编号</param>
    /// <param name="tzid">当前套帐id</param>
    /// <param name="id">物料卡id</param>
    /// <param name="chdm">材料代码</param>
    [WebMethod(Description = "保存物料卡信息")]
    public string SaveWlkInfo(DataTable info, string username, string dhtzdbh, string tzid, string id, string chdm)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            id = (id.Length == 0 ? "0" : id);
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = "", ret_str_sql = "";
                //有可能还是旧的应用,那么需要使用旧的
                if (info.Columns.Contains("chdm"))
                {
                    DataTable DistTable = info.DefaultView.ToTable(true, "chdm");

                    int index = 0;
                    foreach (DataRow ch in DistTable.Rows)
                    {
                        str_sql += @"
                            DECLARE @bh{4} VARCHAR(50),@id{4} int
                            SELECT @bh{4}=left(REPLACE(CONVERT(varchar(12), getdate(), 120),'-',''),6) + isnull((select RIGHT(MAX(wlkbh),4)
                            from cl_t_wlkb 
                            WHERE left(REPLACE(CONVERT(varchar(12), slrq, 120),'-',''),6)=left(REPLACE(CONVERT(varchar(12), getdate(), 120),'-',''),6)),'0000')
                                        
                            IF '{0}'='0'
                            begin
                                INSERT INTO dbo.cl_t_wlkb(tzid,chdm,wlkbh,dhdbh,slr,slrq) VALUES  ('{5}','{1}',cast(@bh{4} as bigint)+1,'{2}','{3}',GETDATE())
                                SELECT @id{4}=SCOPE_IDENTITY()
                            end
                            else
                            begin
                                UPDATE cl_t_wlkb SET chdm='{1}',dhdbh='{2}',xgr='{3}',xgrq=GETDATE() WHERE id='{0}' 
                                SELECT @id{4}={0}
                            end
                    
                            DELETE FROM cl_t_wlkmxb WHERE id=@id{4}    
                        ";
                        int mxIndex = 0;
                        foreach (DataRow dr in info.Select("chdm='" + ch["chdm"].ToString() + "'"))
                        {
                            str_sql += " INSERT INTO dbo.cl_t_wlkmxb(id,tm,sl,xh) VALUES(@id{4},'" + dr["tm"] + "','" + dr["sl"] + "','" + (mxIndex + 1) + "') ";
                            mxIndex++;
                        }
                        str_sql = string.Format(str_sql, id, chdm, dhtzdbh, username, index, tzid);

                        if (index == 0)
                        {//只返回第一个
                            ret_str_sql = @" 
                                SELECT a.id,a.wlkbh,b.tm,b.sl,b.xh,a.chdm,c.dw,chmc
                                FROM cl_t_wlkb a
                                INNER JOIN dbo.CL_T_chdmb c ON a.chdm = c.chdm 
                                LEFT JOIN cl_t_wlkmxb b on a.id=b.id 
                                WHERE a.id=@id{0}   
                                ORDER BY b.xh DESC   
                            ";
                            ret_str_sql = string.Format(ret_str_sql, index);
                        }
                        index++;
                    }
                    str_sql += ret_str_sql;
                }
                else
                {
                    str_sql = @"
                        DECLARE @bh VARCHAR(50),@id int
                        SELECT @bh=left(REPLACE(CONVERT(varchar(12), getdate(), 120),'-',''),6) + isnull((select RIGHT(MAX(wlkbh),4)
                        from cl_t_wlkb 
                        WHERE left(REPLACE(CONVERT(varchar(12), slrq, 120),'-',''),6)=left(REPLACE(CONVERT(varchar(12), getdate(), 120),'-',''),6)),'0000')
                                        
                        IF NOT EXISTS (SELECT * FROM cl_t_wlkb WHERE id='{0}')
                        begin
                            INSERT INTO dbo.cl_t_wlkb(chdm,wlkbh,dhdbh,slr,slrq) VALUES  ('{1}',cast(@bh as bigint)+1,'{2}','{3}',GETDATE())
                            SELECT @id=SCOPE_IDENTITY()
                        end
                        else
                        begin
                            UPDATE cl_t_wlkb SET chdm='{1}',dhdbh='{2}',xgr='{3}',xgrq=GETDATE() WHERE id='{0}' 
                            SELECT @id={0}
                        end
                    
                        DELETE FROM cl_t_wlkmxb WHERE id=@id    
                    ";
                    str_sql = string.Format(str_sql, id, chdm, dhtzdbh, username);
                    for (int i = 0; i < info.Rows.Count; i++)
                        str_sql += " INSERT INTO dbo.cl_t_wlkmxb(id,tm,sl,xh) VALUES(@id,'" + info.Rows[i]["tm"] + "','" + info.Rows[i]["sl"] + "','" + (i + 1) + "') ";

                    str_sql += @" 
                        SELECT a.id,a.wlkbh,b.tm,b.sl,b.xh,a.chdm
                        FROM cl_t_wlkb a
                        LEFT JOIN cl_t_wlkmxb b on a.id=b.id 
                        WHERE a.id=@id   
                        ORDER BY b.xh DESC   
                    ";
                }
                //rtMsg = rtMsg = "error:" + str_sql;
                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);

                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "cl_t_wlkmxb";
                        rtMsg = SerializeDataTableXml(dt);
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + str_sql);
                }
            }
        }

        return rtMsg;
    }

    //获取某材料在某仓库的仓位
    [WebMethod(Description = "获取某材料在某仓库的仓位")]
    public string GetClZCkDCw(string tmcode, string ckid)
    {
        string rtMsg = "";
        string isTm = "";
        string myChdm = "";
        string myKh = "";

        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            //判断扫的是否条码
            string sql = " select chdm,ddh from cl_t_wltmb where tm='" + tmcode + "'";
            DataTable dt = null;
            string errInfo = dal.ExecuteQuery(sql, out dt);
            if (errInfo == "")
            {
                writeLog(dt.Rows.Count.ToString());
                if (dt.Rows.Count > 0)
                {
                    isTm = "1";
                    myChdm = dt.Rows[0][0].ToString();
                    myKh = dt.Rows[0][1].ToString();
                }
                else
                {
                    isTm = "0";
                }
            }

            string str_sql = @"            
                SELECT TOP 4 cw.chdm,cw.ckid,cw.cw into #cw
                from cl_t_cwxxb cw
                inner join cl_t_wltmb wl on cw.chdm=wl.chdm 
                where cw.tzid=@zbid and cw.ckid=@ckid and wl.tm=@tmcode 
                and isnull(cw.cw,'')<>''
                GROUP BY cw.ckid,cw.chdm,cw.cw
            
                select convert(char(8),a.id)+'|'+a.chdm+'|'+isnull(a.bz,'')+'|'+CONVERT(varchar, a.rq, 102)+'|'+isnull(b.chmc,'')+
                '|'+isnull(b.dw,'')+'|'+isnull(b.fk,'')+'|'+isnull(b.kez,'')+'|'+isnull(a.fs,'')+'|'+isnull(a.gh,'')+'|'+
                isnull(a.zl,'')+'|'+isnull(a.mlbz,'')+'|'+isnull(a.tmdw,'')+'|'+convert(char(2),a.tmlb)+'|'+isnull(a.fk,'')+'|'+
                isnull(a.kz,'')+'|'+isnull(a.ddh,'')+'|'+cast(isnull(a.sl,0) as varchar)+'|'+(SELECT cw+',' FROM #cw FOR XML path(''))
                from cl_t_wltmb a inner join CL_T_chdmb b on a.chdm=b.chdm and b.tzid=@zbid where tm=@tmcode
                
                DROP TABLE #cw
            ";
            //20190506 ZHMH 材料仓位信息维护 ，如果刷箱号，没打勾【按款】，那是应该按材料+款号来先中，第一个数据源应该取款号而不是订单号
            string str_sqlxm = @" SELECT a.chdm,sp.spkh scddbh
                FROM dbo.cl_v_dddjmx a
                INNER JOIN cl_t_wlzxd wl ON a.id=wl.lyid AND a.mxid = wl.lymxid 
INNER JOIN dbo.YX_T_Spcgjhb jh ON jh.cggzh=a.scddbh
	INNER JOIN dbo.YX_T_Spdmb sp ON sp.sphh=jh.sphh
                where wl.zxxh =@tmcode
                group by  sp.spkh,a.chdm                
                UNION 
                SELECT a.chdm,a.ddh scddbh
                FROM dbo.cl_t_wltmb a 
                INNER JOIN cl_t_wlsmjlb b ON a.tm=b.sm AND a.zxxh=b.zxxh
                WHERE b.zxxh=@tmcode
                group by a.ddh,a.chdm";

            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@zbid", 1));
            paras.Add(new SqlParameter("@tmcode", tmcode));
            paras.Add(new SqlParameter("@ckid", ckid));
            dt = null;
            if (isTm == "1")
            {
                errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        if (dt.Rows[0][0].ToString().Length > 0)
                        {
                            rtMsg = dt.Rows[0][0].ToString() + "|istm";
                        }
                        else
                        {
                            rtMsg = myChdm + "|" + myKh + "|istm";
                        }
                    }
                    else
                        rtMsg = "Error:查询不到该条码信息！";
                }
                else
                {
                    rtMsg = "Error:查询条码信息时出错！" + errInfo;
                }
            }
            else if (isTm == "0")
            {
                errInfo = dal.ExecuteQuerySecurity(str_sqlxm, paras, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "xhxx";
                        rtMsg = SerializeDataTableXml(dt);
                        dt = null;
                    }
                    else
                    {
                        rtMsg = "Error:查询不到该箱码信息！";
                    }
                }
                else
                {
                    rtMsg = "Error:查询条码信息时出错！" + errInfo;
                }
            }
        }
        return rtMsg;
    }

    //获取款号
    [WebMethod(Description = "获取款号")]
    public string getKHXX(string jhdTm)
    {
        string rtMsg = "", errInfo = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            string str_sql = @" 
		SELECT DISTINCT TOP 1 sp.spkh,kh.khmc,d.djh
		FROM cl_t_dddjb a
		INNER JOIN cl_t_dddjmx b ON a.id=b.id
		INNER JOIN cl_t_dddjmx c ON b.lymxid=c.mxid
		INNER JOIN cl_t_dddjb d ON c.id=d.id
		INNER join yx_T_spcgjhb cg on cg.cggzh=c.scddbh
		INNER join YX_T_Spdmb sp on sp.sphh=cg.sphh
		INNER JOIN yx_t_khb kh ON d.khid=kh.khid
		WHERE a.id={0}
	    ";
            DataTable dt2 = null;
            str_sql = string.Format(str_sql, jhdTm);
            errInfo = dal.ExecuteQuery(str_sql, out dt2);
            if (errInfo == "")
            {
                if (dt2.Rows.Count > 0)
                {
                    rtMsg = dt2.Rows[0]["spkh"].ToString() + "|" + dt2.Rows[0]["khmc"].ToString() + "|" + dt2.Rows[0]["djh"].ToString();
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
            }
        }
        return rtMsg;
    }

    //获取材料仓库信息
    [WebMethod(Description = "获取材料仓库信息")]
    public string getCHCKXX(string tzid)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @" select cast(id as varchar) as dm,mc,dm as xh from cl_V_ckdmb  where tzid={0} and ty=0 
                                    union all select '' as dm,'全部' as mc,'ZZ' as xh 
                                    order by xh ";
                DataTable dt2 = null;
                str_sql = string.Format(str_sql, tzid);
                errInfo = dal.ExecuteQuery(str_sql, out dt2);
                if (errInfo == "")
                {
                    if (dt2.Rows.Count > 0)
                    {
                        dt2.TableName = "cl_V_ckdmb";
                        rtMsg = SerializeDataTableXml(dt2);
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                }
            }
        }

        return rtMsg;
    }

    //设置材料的仓位信息
    [WebMethod(Description = "设置材料的仓位信息")]
    public string setCLInfo2String(string cwInfoStr, string userName, string tzid, string qwtm, string comdjlx, string chtm, string xzr, string combl)
    {

        // 将JSON字符串转换为DJInfo对象
        JsonSerializerSettings setting = new JsonSerializerSettings();
        setting.NullValueHandling = NullValueHandling.Ignore;
        List<DJInfo2String> djInfoList = JsonConvert.DeserializeObject<List<DJInfo2String>>(cwInfoStr,setting);
        DataTable cwInfo = new DataTable();
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-tmValue:123");
        // 添加列
        cwInfo.Columns.Add("scddbh", typeof(string));
        cwInfo.Columns.Add("chdm", typeof(string));
        cwInfo.Columns.Add("sl", typeof(decimal));
        cwInfo.Columns.Add("mxid", typeof(int));
        cwInfo.Columns.Add("ckid", typeof(int));
        cwInfo.Columns.Add("cwid", typeof(int));
        cwInfo.Columns.Add("lyscddbh", typeof(int));
        cwInfo.Columns.Add("dfckid", typeof(int));
        cwInfo.Columns.Add("djlx", typeof(int));
        cwInfo.Columns.Add("tm", typeof(string));
        cwInfo.Columns.Add("esl", typeof(decimal));

        // 添加数据行
        foreach(DJInfo2String dr in djInfoList)
        {
            //新建行的赋值
            DataRow dtr = cwInfo.NewRow();//创建新行
            dtr["scddbh"] = dr.scddbh;
            dtr["chdm"] = dr.chdm;
            dtr["sl"] = dr.sl;
            dtr["mxid"] = dr.mxid;
            dtr["ckid"] = dr.ckid;
            dtr["cwid"] = dr.cwid;
            dtr["lyscddbh"] = dr.lyscddbh;
            dtr["dfckid"] = dr.dfckid;
            dtr["djlx"] = dr.djlx;
            dtr["tm"] = dr.tm;
            dtr["esl"] = dr.esl;
            cwInfo.Rows.Add(dtr);
        }
        return setCLInfo(cwInfo, userName, tzid, qwtm, comdjlx, chtm, xzr, combl);
    }


    //设置材料的仓位信息
    [WebMethod(Description = "设置材料的仓位信息")]
    public string setCLInfo(DataTable cwInfo, string userName, string tzid, string qwtm, string comdjlx, string chtm, string xzr, string combl)
    {

        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-cwInfo.Columns:"+cwInfo.Columns);
        DataRow[] testDr = cwInfo.Select("", "mxid");
        foreach (DataColumn column in cwInfo.Columns)
        {
            nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-cwInfo.Columns_value:"+column.ColumnName);
            if (testDr[0][column.ColumnName] == null)
            {
                nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-cwInfo."+column.ColumnName+":woshinull");
            }else  nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-cwInfo."+column.ColumnName+":"+testDr[0][column.ColumnName]);
        }
        string rtMsg = "", errInfo = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            if (cwInfo != null)
            {
                //增加刷码记录
                int djlx = 2460;
                if (comdjlx == "材料条码")
                {//如果是刷条码,不是刷单据
                    djlx = 2461;
                }


                DataRow[] dr = cwInfo.Select("", "mxid");
                DataTable dt = null;
                DataTable idt = null;
                StringBuilder sql = new StringBuilder();

                string errmsg = "";
                List<SqlParameter> para = new List<SqlParameter>();
                StringBuilder sb = new StringBuilder();
                sb.Append(" DECLARE @userName VARCHAR(50),@cwid int,@i INT,@num INT ,@cws varchar(max),@qw varchar(max),@id int; SELECT @userName=cname,@cwid=0 FROM t_user WHERE NAME='" + userName + "'; ");
                sb.Append(" declare @fcbl decimal(9,3); select distinct items ryid into #xzry from dbo.f_Split('" + xzr + "',','); ");
                sb.Append(" select @fcbl= case " + combl + " when 1 then CAST(ROUND(1.0/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) ELSE CAST(ROUND(0.9/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) END; ");
                if (djlx == 2461)
                {
                    for (int i = 0; i < dr.Length; i++)
                    {   //判断条码是否已经维护过
                        sql.Append(" select * from cl_t_pdakcdj where tm='" + dr[i]["tm"] + "'and tzid='" + tzid + "' and djlx='" + djlx + "' and chdm='" + dr[i]["chdm"] + "' and gzh='" + dr[i]["scddbh"] + "' ");
                        if (i < dr.Length - 1)
                        {
                            sql.Append(" union all ");
                        }
                    }
                    errmsg = dal.ExecuteQuery(sql.ToString(), out idt);
                    DataRow[] htm = idt.Select("", "tm");
                    if (errmsg == "")
                    {
                        if (idt.Rows.Count > 0)
                        {
                            return "havetm:该条码已经保存过!\r\n条码：" + htm[0]["tm"] + ",\r\n材料：" + htm[0]["chdm"] + ",\r\n跟踪号：" + htm[0]["gzh"];
                        }
                    }
                    else
                    {
                        rtMsg = "error:" + errmsg;
                        writeLog(rtMsg + "\r\n" + sql);
                        return rtMsg;
                    }
                }
                List<string> tmdtcwList = new List<string>();
                for (int i = 0; i < dr.Length; i++)
                {
                    if (string.IsNullOrEmpty(dr[i]["scddbh"].ToString()))
                    {
                        rtMsg = "error: 条码:" + dr[i]["tm"] + " 没有跟踪号！";
                        return rtMsg;
                    }

                    sb.Append("  SELECT @cwid=isnull(id,0) FROM dbo.cl_t_cwxxb WHERE chdm='" + dr[i]["chdm"] + "' AND gzh='" + dr[i]["scddbh"] + "' and tzid='" + tzid + "' AND ckid='" + dr[i]["ckid"] + "' ");

                    //sb.Append( " IF @cwid=0 ");
                    sb.Append(" IF not exists ( SELECT 1 FROM dbo.cl_t_cwxxb WHERE chdm='" + dr[i]["chdm"] + "' AND gzh='" + dr[i]["scddbh"] + "' and tzid='" + tzid + "' AND ckid='" + dr[i]["ckid"] + "' ) ");
                    sb.Append("    insert into cl_t_cwxxb (gzh,chdm,cw,tzid,xgrq,xgr,ckid) values('" + dr[i]["scddbh"] + "','" + dr[i]["chdm"] + "','" + qwtm + "','" + tzid + "',GETDATE(),@userName,'" + dr[i]["ckid"] + "'); ");
                    sb.Append(" else ");
                    sb.Append(" begin ");
                    sb.Append("       SET @i=1; ");
                    sb.Append("       SELECT @num=COUNT(id) FROM [dbo].SplitToTable('" + qwtm + "', '/') ");
                    sb.Append("       SELECT @qw=value FROM [dbo].SplitToTable('" + qwtm + "', '/') WHERE id=@i ");
                    sb.Append("       WHILE (@qw<>'' and @i<=@num)  ");
                    sb.Append("       begin   ");
                    sb.Append("          SELECT @cws=isnull(cw,'') FROM dbo.cl_t_cwxxb WHERE id=@cwid ");
                    sb.Append("          IF CHARINDEX(@qw,@cws,0)=0 ");
                    sb.Append("             IF @cws='' ");
                    sb.Append("                UPDATE cl_t_cwxxb SET gzh='" + dr[i]["scddbh"] + "',chdm='" + dr[i]["chdm"] + "',cw=@qw,tzid='" + tzid + "',xgrq=GETDATE(),xgr=@userName,ckid='" + dr[i]["ckid"] + "' WHERE id=@cwid ");
                    sb.Append("             else ");
                    sb.Append("                UPDATE cl_t_cwxxb SET gzh='" + dr[i]["scddbh"] + "',chdm='" + dr[i]["chdm"] + "',cw=cw+'/'+@qw,tzid='" + tzid + "',xgrq=GETDATE(),xgr=@userName,ckid='" + dr[i]["ckid"] + "' WHERE id=@cwid ");
                    sb.Append("          SET @i=@i+1 ");
                    sb.Append("          SELECT @qw=value FROM [dbo].SplitToTable('" + qwtm + "', '/') WHERE id=@i ");
                    sb.Append("       end ");
                    sb.Append(" end ");


                    string uuid = System.Guid.NewGuid().ToString("N");
                    sb.Append(" insert into cl_T_pdaxzry ( uuid, ryid, fcbl ) select '" + uuid + "' ,ryid,@fcbl from #xzry ");

                    //多仓位按仓位排序再保存
                    string[] qwtmArr = qwtm.Split('/');
                    Array.Sort(qwtmArr);
                    tmdtcwList.Add(" select '" + tzid + "' tzid,'" + dr[i]["ckid"] + "' ckid," + djlx + " djlx,'" + dr[i]["tm"] + "' tm,'" + dr[i]["chdm"] + "' chdm,'" + dr[i]["scddbh"] + "' gzh,'" + dr[i]["sl"] + "' sl,'" + string.Join(",", qwtmArr) + "' cw,'" + dr[i]["mxid"] + "' lymxid,'" + dr[i]["djlx"] + "' lydjlx,'" + userName + "' zdr,getdate() zdrq,1 shbs,1 qrbs,1 djbs,getdate() rq,'" + uuid + "' uuid ");

                    //sb.Append(" insert cl_T_pdakcdj(tzid,ckid,djlx,tm,chdm,gzh,sl,cw,lymxid,lydjlx,zdr,zdrq,shbs,qrbs,djbs,rq) values('" + tzid + "','" + dr[i]["ckid"] + "'," + djlx + ",'" + dr[i]["tm"] + "','" + dr[i]["chdm"] + "','" + dr[i]["scddbh"] + "','" + dr[i]["sl"] + "','" + string.Join(",", qwtmArr) + "','" + dr[i]["mxid"] + "','" + dr[i]["djlx"] + "','" + userName + "',getdate(),1,1,1,getdate())");
                    //sb.Append(" SET @id=SCOPE_IDENTITY(); ");
                    //sb.Append(" exec pda_kc_kchz @id,'sl+',0; ");
                }
                sb.Append(" declare @getdate datetime;set @getdate=getdate(); declare @ids varchar(max); ");
                sb.Append(" insert cl_T_pdakcdj(tzid,ckid,djlx,tm,chdm,gzh,sl,cw,lymxid,lydjlx,zdr,zdrq,shbs,qrbs,djbs,rq,guid) " + string.Join(" union all ", tmdtcwList.ToArray())) ;
                sb.Append(" set @ids=(select CAST(id AS VARCHAR(max))+','  from cl_t_pdakcdj where djlx='" + djlx + "' and zdr='" + userName + "' and zdrq=@getdate FOR XML PATH('') ); exec pda_kc_kchz @ids,'sl+',0; ");

                string mysql = "";
                if (djlx == 2460) {
                    mysql += " select * into #aa from (" + string.Join(" union all ", tmdtcwList.ToArray())+ ") a " ;
                    mysql += " SELECT TOP 1 a.* FROM dbo.cl_T_pdakcdj a ";
                    mysql += " INNER JOIN #aa b ON a.tm=b.tm AND a.chdm=b.chdm AND a.gzh=b.gzh AND a.lymxid=b.lymxid AND a.lydjlx=b.lydjlx ";
                    mysql += " WHERE a.djlx=2460 ";

                    errmsg = dal.ExecuteQuery(mysql, out idt);
                    DataRow[] datacf = idt.Select("", "tm");
                    if (errmsg == "")
                    {
                        if (idt.Rows.Count > 0)
                        {
                            return "havetm:该条码已经保存过!\r\n条码：" + datacf[0]["tm"] + ",\r\n材料：" + datacf[0]["chdm"] + ",\r\n跟踪号：" + datacf[0]["gzh"];
                        }
                    }
                    else
                    {
                        rtMsg = "error:" + errmsg;
                        return rtMsg;
                    }
                }

                errInfo = dal.ExecuteQuerySecurity(sb.ToString(), para, out dt);

                if (errInfo == "")
                {
                    rtMsg = "保存成功！";
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + sb.ToString());
                }
            }
            else
            {
                rtMsg = "error:数据不存在！";
            }
        }
        return rtMsg;
    }

    //获取要维护仓位的材料信息
    [WebMethod(Description = "获取要维护仓位的材料信息")]
    public string getCLInfo(string djid, string djly, string tzid, string ckid)
    {
        string rtMsg = "", errInfo = "";

        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            if (tzid == "")
            {
                rtMsg = "error:tzid参数为空！";
            }
            else
            {
                string str_sql = "";
                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();

                if (djly == "1" || djly == "S")//到货通知单
                {
                    if (djly == "S")
                    {
                        str_sql = @" declare @djids varchar(max) 
                                   SELECT @djids=ids FROM  cl_t_cwdjtmIDgl WHERE id=@djid
                                   IF(LEN(@djids)!=0)
                                     SELECT  @djids=SUBSTRING(@djids,2,LEN(@djids))
                                   ELSE 
                                     SELECT @djids=0
                                     
                                   select a.* into #myzb from cl_v_dddjmx a where a.id in (SELECT value FROM dbo.SplitToTable(@djids,','))               
                                   select distinct a.scddbh,a.chdm,a.sl,a.mxid,a.ckid,xx.id cwid,0 lyscddbh,0 dfckid,a.djlx,@djid tm,isnull(pd.sl,0) esl  
                                   from #myzb a 
                                   inner join cl_v_chdmb c on a.chdm=c.chdm 
                                   left join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid  
                                   left join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=a.chdm and xx.gzh=a.scddbh and xx.ckid=a.ckid 
                                   left join (  SELECT SUM(sl) sl,lymxid,chdm,gzh FROM cl_t_pdakcdj 
                                                 GROUP BY lymxid,chdm,gzh) pd on a.mxid=pd.lymxid and a.chdm=pd.chdm and a.scddbh=pd.gzh
                                   where c.tzid=1 and a.djlx=624
                                   order by a.mxid 
                                   drop table #myzb ";

                    }
                    else
                    {
                        str_sql = @" 
                                  SELECT b.*
                                  into #myzb 
                                  FROM CL_T_dddjb a 
                                  inner JOIN cl_v_dddjmx b ON a.id=b.id
                                  where (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))=@djid
                                  AND a.djlx=624
                                                              
                                  select distinct a.scddbh,a.chdm,a.sl,a.mxid,a.ckid,isnull(xx.id,0) cwid,0 lyscddbh,0 dfckid,a.djlx,@djid tm, isnull(pd.sl,0) esl    
                                  from #myzb a 
                                  inner join cl_v_chdmb c on a.chdm=c.chdm 
                                  left join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid  
                                  left join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=a.chdm and xx.gzh=a.scddbh and xx.ckid=a.ckid 
                                  left join (  SELECT SUM(sl) sl,lymxid,chdm,gzh FROM cl_t_pdakcdj 
                                                 GROUP BY lymxid,chdm,gzh) pd on a.mxid=pd.lymxid and a.chdm=pd.chdm and a.scddbh=pd.gzh
                                  where c.tzid=1 and a.djlx=624
                                  order by a.mxid 
                                  drop table #myzb  ";
                    }

                    para.Add(new SqlParameter("@djid", djid));
                    //para.Add(new SqlParameter("@jsrq", jsrq));
                    para.Add(new SqlParameter("@tzid", tzid));
                    writeLog(rtMsg+ "\r\n"+djid + "\r\n" + str_sql);

                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }
                else if (djly == "2")//计划领用单（退领）
                {
                    //6452：退领     
                    str_sql = @" 
                              select distinct a.scddbh,a.chdm,a.sl,a.mxid,a.ckid,isnull(xx.id,0) cwid,0 lyscddbh,0 dfckid ,a.djlx,@djid tm,0 esl
                              from cl_v_dddjmx a 
                              inner join cl_t_dddjmx b on a.mxid=b.lymxid
                              INNER JOIN cl_T_dddjb d on b.id=d.id and d.djlx=2250
                              inner join cl_v_chdmb c on a.chdm=c.chdm and c.tzid=1 
                              inner join cl_t_chdmb cc on c.chdm=cc.chdm 
                              inner join cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid
                              left join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=ch.chdm and a.scddbh=xx.gzh and xx.ckid=a.ckid 
                              where a.djlx=605 AND a.djlb=6452 and d.id=@djid                               
                              ORDER BY a.mxid  ";

                    para.Add(new SqlParameter("@djid", djid));
                    //para.Add(new SqlParameter("@jsrq", jsrq));
                    para.Add(new SqlParameter("@tzid", tzid));
                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }
                else if (djly == "3")//调拨单
                {
                    str_sql = @"  
                              select distinct isnull(a.lyscddbh,0) scddbh,a.chdm,a.sl,a.mxid,isnull(a.dfckid,0) ckid,xx.id cwid,isnull(a.lyscddbh,0) lyscddbh,isnull(a.dfckid,0) dfckid ,a.djlx,@djid tm ,isnull(pd.sl,0) esl 
                              from cl_v_kcdjmx a  
                              inner join cl_v_chdmb c on a.chdm=c.chdm and c.tzid=1 
                              inner join cl_t_chdmb cc on c.chdm=cc.chdm 
                              inner join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid
                              inner join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=ch.chdm and a.lyscddbh=xx.gzh and xx.ckid=a.dfckid 
                              left join (  SELECT SUM(sl) sl,lymxid,chdm,gzh FROM cl_t_pdakcdj 
                                                 GROUP BY lymxid,chdm,gzh) pd on a.mxid=pd.lymxid and a.chdm=pd.chdm and a.scddbh=pd.gzh
                              where  a.id=@djid AND a.djlx=561
                              order by a.mxid ";

                    para.Add(new SqlParameter("@djid", djid));
                    //para.Add(new SqlParameter("@jsrq", jsrq));
                    para.Add(new SqlParameter("@tzid", tzid));
                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }
                else if (djly == "0")//按仓位材料查询
                {
                    //                    str_sql = @" SELECT t1.gzh scddbh,t1.chdm,isnull(t1.sl,0) as sl,t1.id mxid,t1.ckid,isnull(cw.id,0) cwid,0 lyscddbh,0 dfckid ,0 djlx 
                    //                              from cl_T_chgzhkcmx as t1
                    //                              inner join cl_v_chdmb as ch ON t1.chdm=ch.chdm and t1.tzid=@tzid
                    //                              inner join cl_t_wltmb wl on t1.chdm=wl.chdm
                    //                              inner join yx_t_ckdmb as ck on t1.ckid=ck.id                               
                    //                              LEFT JOIN cl_t_cwxxb AS cw on t1.chdm=cw.chdm and t1.gzh=cw.gzh  and t1.ckid=cw.ckid  and cw.tzid=@tzid
                    //                              where t1.tzid=@tzid and wl.tm=@djid and t1.ckid=@ckid and (isnull(cw.id,0)=0 or cw.cw='') and isnull(t1.sl,0)>0 ";


                    //材料条码 0，物料卡号 1，箱
                    str_sql = @" 
                            CREATE TABLE #tmp(
	                            [scddbh] [varchar](500) NULL,
	                            [chdm] [varchar](20) NULL,
	                            [sl] [float] NULL,
	                            [mxid] [int] NOT NULL,
	                            [ckid] [int] NOT NULL,
	                            [cwid] [int] NOT NULL,
	                            [lyscddbh] [int] NOT NULL,
	                            [dfckid] [int] NOT NULL,
	                            [djlx] [int] NOT NULL,
	                            [tm] [varchar](30) NOT NULL,
                                [esl] [int] NULL
                                )
                                    SELECT * into #info FROM (
                                      select a.ddh scddbh,a.chdm,ISNULL(a.sl,0) sl,a.id mxid,@ckid ckid,0 cwid,0 lyscddbh,0 dfckid ,0 djlx,@djid tm ,0 esl
                                      from cl_t_wltmb a                                 
                                      where a.tm=@djid
                                      union                        
                                      SELECT c.ddh scddbh,c.chdm,SUM(b.sl) sl ,0 mxid,@ckid ckid,0 cwid,0 lyscddbh,0 dfckid ,1 djlx,@djid tm,0 esl
                                      FROM cl_t_wlkb a
                                      LEFT JOIN   cl_t_wlkmxb b on a.id=b.id 
                                      INNER JOIN dbo.cl_t_wltmb c ON b.tm=c.tm
                                      WHERE a.wlkbh=@djid 
                                      GROUP BY c.chdm,c.ddh
                                      union
                                      SELECT a.scddbh,a.chdm,sum(wl.sl) sl,0 mxid,@ckid ckid,0 cwid,0 lyscddbh,0 dfckid ,2 djlx,@djid tm, 0 esl
                                      FROM dbo.cl_v_dddjmx a
                                      INNER JOIN cl_t_wlzxd wl ON a.id=wl.lyid AND a.mxid = wl.lymxid 
                                      where wl.zxxh =@djid
                                      group by a.scddbh,a.chdm
                                    ) a

                                    IF EXISTS (SELECT 1 FROM #info)
                                       insert #tmp
                                       SELECT * FROM #info
                                    else
                                       insert #tmp
                                       SELECT a.ddh scddbh,a.chdm,sum(a.sl) sl,0 mxid,@ckid ckid,0 cwid,0 lyscddbh,0 dfckid ,2 djlx,@djid tm,0 esl 
                                       FROM dbo.cl_t_wltmb a 
                                       INNER JOIN cl_t_wlsmjlb b ON a.tm=b.sm AND a.zxxh=b.zxxh
                                       WHERE b.zxxh=@djid
                                       group by a.ddh,a.chdm

                                      select * from #tmp;
                                      drop table #tmp;
                               ";

                    para.Add(new SqlParameter("@ckid", ckid));
                    para.Add(new SqlParameter("@djid", djid));
                    para.Add(new SqlParameter("@tzid", tzid));
                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);

                }

                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        if (djly == "1" || djly == "S")
                        {
                            dt.TableName = "cl_v_dddjmx";
                            rtMsg = SerializeDataTableXml(dt);

                            dt = null;
                        }
                        else if (djly == "2")
                        {
                            dt.TableName = "cl_v_dddjmx";
                            rtMsg = SerializeDataTableXml(dt);
                            dt = null;
                        }
                        else if (djly == "3")
                        {
                            dt.TableName = "cl_v_kcdjmx";
                            rtMsg = SerializeDataTableXml(dt);
                            dt = null;
                        }
                        else if (djly == "0")
                        {
                            dt.TableName = "cl_T_chgzhkcmx";
                            rtMsg = SerializeDataTableXml(dt);
                            dt = null;

                        }
                        else
                        {
                            rtMsg = "error:该单据类型不存在！(" + djly + ")";
                        }
                    }
                    else
                    {
                        rtMsg = "error:查询无记录！";
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + str_sql);
                }
            }
        }
        return rtMsg;
    }

    //加载主表数据 数据源是物料发货计划(cl_t_dddjb.djlx=605)
    [WebMethod(Description = "加载主表数据 数据源是物料发货计划")]
    public string getZXDList(string ksrq, string jsrq, string tzid, string khid)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @"  
                   SELECT DISTINCT a.id,b.id tmid
                   INTO #tmInfo
                   FROM cl_v_dddjmx a
                   LEFT JOIN cl_v_dddjmx b ON a.mxid=b.lymxid  AND b.djlx=2250
                   where a.tzid=@tzid and a.rq>=@ksrq and a.rq<dateadd(day,1,@jsrq) and a.djlx=605
                              
                   select a.djh,a.khid,kh.khdm,kh.khmc,(select sum(sl) from cl_v_dddjmx where djlx=605 and id=a.id group by id) sl,isnull(ht.ddbh,'') ddbh,
                      g.mc djlbmc,a.id,a.sfjj,
                      CAST(a.id AS VARCHAR(50))+','+(SELECT DISTINCT CAST(a1.tmid AS VARCHAR(50)) + ',' FROM #tmInfo a1 WHERE a1.id=a.id FOR XML PATH('')) tm
                   into #myzb
                   from cl_t_dddjb a
                   inner join yx_t_khb kh on kh.khid=a.khid
                   inner join yx_v_djlb g on a.djlb=g.id and g.tzid=a.tzid 
                   left join zw_t_htdddjb ht on ht.id=a.htddid
				   LEFT JOIN cl_t_dddjb b ON b.djlx=624 AND a.lydjid=b.id AND a.lydjlx=b.djlx AND b.djlb=6678 
                   where a.tzid=@tzid and a.rq>=@ksrq and a.rq<dateadd(day,1,@jsrq) and a.djlx=605  AND b.id IS NULL
                ";
                if (khid != "")
                    str_sql += " and a.khid='" + khid + "' order by a.rq desc;";
                else
                    str_sql += " order by a.rq desc;";

                str_sql += @"
                   select a.djh,a.khmc,a.sl,isnull(a.sl,0)-isnull(b.zxsl,0) sysl,a.ddbh,a.djlbmc,a.id,a.khid,a.khdm,a.tm,hyxx.yffkfs,hyxx.ckdz
                   from #myzb a
                   left join (
                      select sum(a.sl) zxsl,a.lyid from cl_t_wlzxd a inner join #myzb b on a.lyid=b.id group by a.lyid
                   )b on a.id=b.lyid 
                   left join (
                       SELECT  hy.id , hy.ckdz , yffk.mc yffkfs from yx_T_khb_hyxx hy 
                       LEFT JOIN cl_V_yffkfs yffk ON yffk.dm = hy.yffkfs 
                   ) hyxx on a.sfjj=hyxx.id
                   where a.sl>0;
                   
                   drop table #myzb;drop table #tmInfo;
                ";

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@ksrq", ksrq));
                para.Add(new SqlParameter("@jsrq", jsrq));
                para.Add(new SqlParameter("@tzid", tzid));
                errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "cl_t_wlzxd";
                        rtMsg = SerializeDataTableXml(dt);
                    }
                    else
                        rtMsg = "error:查询无记录！";
                }
                else
                    rtMsg = "error:" + errInfo;
            }
        }

        return rtMsg;
    }


    [WebMethod(Description = "五里陈佳发货出库单选择 加载主表数据 数据源是物料发货计划")]
    public Result getZXDListWL(string tmid, string tzid, string exisIDS)
    {
        Result result = new Result();
        string errInfo = "";
        if (tzid == "")
        {
            result.Data = "tzid参数为空！";
            result.Errcode = 100;
            //   rtMsg = "error:tzid参数为空！";
        }
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                //先假设条码是总部发货计划，
                //如果不是，那可能是条码或者可能是到货通知单(201904100001)
                //  接下来判断如果是条码，那么要取出业务单据（条码是码单或者到货通知单生成的）
                //  如果是到货通知单（201904100001） 那条取出单据信息
                string str_sql = @" 
                   declare @id int; set @id=0;

                   SELECT @id=jz.id FROM dbo.CL_T_dddjb a /*总部发货计划单*/
                   INNER JOIN CL_T_dddjmx b ON a.id=b.id
                   INNER JOIN CL_T_dddjmx jm ON jm.mxid=b.lymxid 
                   INNER JOIN cl_T_dddjb jz ON jz.id=jm.id and jz.djlx=605
                   --INNER JOIN dbo.cl_v_kcdjmx fh ON fh.lymxid=jm.mxid AND fh.lydjid=jz.id and fh.djlx in (511,512)
                   WHERE a.id='$id$' and a.djlx=2250;
                   
                   if @id>0
                     select a.djh,m.chdm, m.scddbh,(m.sl) sl,isnull(x.sl,0) ysmsl,0 smsl, CONVERT(CHAR(8), a.rq, 112) as rq,a.id,m.mxid,a.djlx,sp.spkh/*顺序一定要注意*/
                     from CL_T_dddjb a                                    
                     inner join CL_T_dddjmx m on m.id=a.id
                     INNER JOIN dbo.YX_T_Spcgjhb jh ON jh.cggzh=m.scddbh
                     INNER JOIN dbo.YX_T_Spdmb sp ON sp.sphh=jh.sphh
                     left join (
                         SELECT a.lymxid,a.lydjid,a.lydjlx,a.tzid,SUM(a.sl) sl FROM dbo.cl_v_kcdjmx a WHERE a.djlx=541 GROUP BY a.lydjid,a.lydjlx,a.tzid,a.lymxid
                     )x on x.lydjid=a.id and x.lydjlx=a.djlx and x.tzid=a.khid and x.lymxid=m.mxid
                     where a.tzid=1 and a.khid=@tzid     and a.shbs=1 and a.id=@id and a.id not in ($eid$)
                     --group by a.djh,a.id,m.chdm,a.rq,sp.spkh
                      order by a.id,m.chdm,(m.sl-isnull(x.sl,0)) desc
                   else if exists (select 1 from cl_t_wltmb where tm='$tmid$')
                      SELECT a.chdm,a.ddh spkh,a.sl,a.lydjlx,a.tm,'tm' tmlx  FROM dbo.cl_t_wltmb a WHERE a.tm='$tmid$'                   
                   else 
                   begin 
                      SELECT a.chdm,c.ddh spkh,b.sl,c.lydjlx,c.tm,'wlk' tmlx 
                      FROM dbo.cl_t_wlkb a
                      INNER JOIN dbo.cl_t_wlkmxb b ON a.id=b.id
                      INNER JOIN dbo.cl_t_wltmb c ON b.tm=c.tm
                      WHERE a.wlkbh='$tmid$'
                      
                   end
                    SELECT TOP 1 1 bs FROM cl_T_pdakcdj WHERE tm='$tmid$' AND tzid=@tzid
                ";


                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@tzid", tzid));
                if (tmid.Length > 10)
                {
                    str_sql = str_sql.Replace("$tmid$", tmid);
                    str_sql = str_sql.Replace("$id$", "-1");
                }
                else
                {
                    str_sql = str_sql.Replace("$tmid$", tmid);
                    str_sql = str_sql.Replace("$id$", tmid);
                }

                str_sql = str_sql.Replace("$eid$", (exisIDS.Length == 0 ? "0" : exisIDS));
                DataSet ds = null;
                errInfo = dal.ExecuteQuerySecurity(str_sql, para, out ds);
                DataTable dt = ds.Tables[0];
                if (errInfo == "")
                {
                    if (ds.Tables[1].Rows.Count > 0)
                    {
                        result.Data = "条码已经刷过！";
                        result.Errcode = 105;
                    }
                    else
                    {
                        if (dt.Rows.Count > 0)
                        {
                            if (dt.Columns.Count > 6)
                                dt.TableName = "cl_v_kcdjmx";
                            else
                                dt.TableName = "cl_t_wltmb";

                            result.Data = SerializeDataTableXml(dt);

                            if (dt.TableName == "cl_t_wltmb")
                            {
                                //判断是否是直发条码
                                if (dt.Rows[0]["tmlx"].ToString() == "tm")
                                {
                                    Result tmp = getZFWLTM(tzid, tmid, exisIDS, dt.Rows[0]["lydjlx"].ToString());
                                    if (tmp.Errcode == 0) result.Data = tmp.Data;
                                }
                            }
                        }
                        else
                        {
                            //判断是否是日期+单据号
                            Result tmp = getZFWLTM(tzid, tmid, exisIDS,"djhrq");
                            if (tmp.Errcode == 0)
                            {
                                result.Data = tmp.Data;
                                result.Errcode = 0;
                            }
                            else
                            {
                                result.Data = "查询无记录！";
                                result.Errcode = 101;
                            }
                        }
                    }
                }
                else
                {
                    result.Data = errInfo;
                    result.Errcode = 200;
                }
            }
        }
        return result;
    }
    /// <summary>
    /// 五里获取直发条码对应的到货单
    /// </summary>
    /// <param name="tm"></param>
    private Result getZFWLTM(string tzid, string tmid, string exisIDS, string lydjlx)
    {
        string errInfo = "";
        Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            string str_sql = "";
            //一定要去重
            if (lydjlx == "2222")
            {//码单生成
                str_sql += @" 
                 SELECT DISTINCT b.* into #dh 
                 FROM cl_t_wltmb x
                 INNER JOIN dbo.cl_v_jhdjmxb a ON  x.lydjlx=a.djlx AND x.lydjid=a.id
                 INNER JOIN dbo.cl_v_dddjmx b ON b.djlx=2380 AND b.sqmxid=a.mxid AND b.sqid=a.id
                 inner join zw_v_cphtddmx  ht on ht.gzh=b.scddbh
                 WHERE x.tm='$tmid$' and b.tzid=1 and ht.khid=@tzid and b.id not in ($eid$)  ;
";
            }
            else if (lydjlx == "0")
            {//这个里面可能是条码，也可能是到货单据
             //如果是条码那一定要装箱，
                str_sql += @" 
                 SELECT  C.* into #dh FROM cl_t_wlzxd A 
INNER JOIN cl_t_wltmb B ON A.zxxh=B.zxxh
INNER JOIN CL_v_DDDJMX C ON C.DJLX=624 AND C.ID=A.LYID AND C.MXID=A.lymxid
WHERE B.TM='$tmid$' and c.id not in ($eid$) 
 
";
            }
            else if (lydjlx == "djhrq")
            {
                str_sql += @"                   
SELECT  C.*  into #dh FROM   CL_v_DDDJMX C  
WHERE c.djlx=624 and  CONVERT(VARCHAR(6),c.rq,112)+c.DJH='$tmid$' and c.id not in ($eid$) 
";
            }
            else
            {//到货单生成
                str_sql += @" 
                 SELECT DISTINCT b.* into #dh 
                 FROM cl_t_wltmb x                 
                 INNER JOIN dbo.cl_v_dddjmx b ON  b.djlx=624 AND x.lydjid=b.id                 
                 WHERE x.tm='$tmid$' and b.tzid=1 and b.zfkhid=@tzid and b.id not in ($eid$)  ;
";
            }
            str_sql += @" 
              select a.djh,a.chdm, a.scddbh,(a.sl) sl,isnull(x.sl,0) ysmsl,0 smsl, CONVERT(CHAR(8), a.rq, 112) as rq,a.id,a.mxid,a.djlx,sp.spkh/*顺序一定要注意*/
                from #dh  a 
                INNER JOIN dbo.YX_T_Spcgjhb jh ON jh.cggzh=a.scddbh
                INNER JOIN dbo.YX_T_Spdmb sp ON sp.sphh=jh.sphh
                left join (
                    SELECT a.lymxid,a.lydjid,a.lydjlx,a.tzid,SUM(a.sl) sl FROM dbo.cl_v_kcdjmx a WHERE a.djlx=541 GROUP BY a.lydjid,a.lydjlx,a.tzid,a.lymxid
                )x on x.lydjid=a.id and x.lydjlx=a.djlx and x.tzid=a.zfkhid and x.lymxid=a.mxid

                select a.chdm,a.ddh spkh,a.sl from cl_t_wltmb a where  a.tm='$tmid$'
";
            str_sql = str_sql.Replace("$tmid$", tmid);
            str_sql = str_sql.Replace("$eid$", (exisIDS.Length == 0 ? "0" : exisIDS));
            DataSet ds = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            errInfo = dal.ExecuteQuerySecurity(str_sql, para, out ds);
            if (errInfo == "")
            {
                //如果第一个表没数据,那表明前面已经刷到这个单据了,直接返回条码数据就可以
                if (ds.Tables[0].Rows.Count > 0)
                {
                    #region
                    //string chdm_ = ds.Tables[1].Rows[0]["chdm"].ToString();
                    //string spkh_ = ds.Tables[1].Rows[0]["ddh"].ToString();
                    //decimal sl_ = decimal.Parse(ds.Tables[1].Rows[0]["sl"].ToString());
                    //decimal sysl_ = sl_;
                    //ds.Tables[0].TableName = "cl_v_kcdjmx";
                    //foreach (DataRow dr in ds.Tables[0].Rows)
                    //{
                    //    if (dr["spkh"].ToString() == spkh_ && dr["chdm"].ToString() == chdm_)
                    //    {
                    //        if (decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString()) > sysl_)
                    //        {
                    //            dr["smsl"] = sysl_.ToString();
                    //            sysl_ = 0;
                    //            break;
                    //        }
                    //        else
                    //        {
                    //            dr["smsl"] = decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString());
                    //            sysl_ = sysl_ - decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString());
                    //        }
                    //    }
                    //}

                    //foreach (DataRow dr in ds.Tables[0].Rows)
                    //{
                    //    if (dr["chdm"].ToString() == chdm_)
                    //    {
                    //        if (decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString()) > sysl_)
                    //        {
                    //            dr["smsl"] = sysl_;
                    //            sysl_ = 0;
                    //            break;
                    //        }
                    //        else
                    //        {
                    //            dr["smsl"] = decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString());
                    //            sysl_ = sysl_ - decimal.Parse(dr["sl"].ToString()) - decimal.Parse(dr["ysmsl"].ToString());
                    //        }
                    //    }
                    //}

                    //if (sysl_ > 0)
                    //{
                    //    result.Errcode = 1003;
                    //    result.Errmsg = "条码的数量比单据数量多";
                    //}
                    //else
                    //{
                    //    result.Data = SerializeDataTableXml(ds.Tables[0]);
                    //}
                    #endregion
                    ds.Tables[0].TableName = "cl_v_kcdjmx_zf";
                    result.Data = SerializeDataTableXml(ds.Tables[0]) + "##" + SerializeDataTableXml(ds.Tables[1]);
                }
                else
                {
                    //判断是否是直发条码
                    result.Data = "查询无记录！";
                    result.Errcode = 102;
                }
            }
            else
            {
                result.Data = errInfo;
                result.Errcode = 201;
            }
        }
        return result;
    }



    [WebMethod(Description = "五里陈佳发货出库单选择 入库保存数据")]
    public Result saveDataWL(string username, string tzid, string data, string tmRecode, string cwtm)
    {

        int djlx = 541;
        int zb_djlb = 2147;//入库别: 01.订单收料
        int zb_ckid = 8253;//仓库 默认01五里车间仓库
        Result result = new Result();
        if (tzid == "1")
        {
            result.Errmsg = "套账错了!";
            result.Errcode = 200;
            return result;
        }
        List<Dictionary<string, string>> dataList = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(data);
        List<TMRecode> tmRecodeList = JsonConvert.DeserializeObject<List<TMRecode>>(tmRecode);
        string sql = @" 
begin try 
Begin TransAction
            declare @djh varchar(50) ;declare @id int  ;declare @khid int ;
            ";
        List<int> idList = new List<int>();
        foreach (Dictionary<string, string> d in dataList)
        {
            if (!idList.Contains(int.Parse(d["id"].ToString())))
                idList.Add(int.Parse(d["id"].ToString()));
        }
        foreach (int lydjid in idList)
        {
            sql += @"  
            select @djh=max(djh)+1  from cl_t_kcdjb where tzid={3} and year(rq)=year(getdate()) and month(rq)=month(getdate()) and djlx={1};
            if @djh is null
                set @djh='100001'

            insert into cl_t_kcdjb (tzid,dhbh,djlx,djlb,djbs,djh,rq,khid,
            shdwid,ckid,dfckid,je,cjje,skje,kpje,zdr,shr,qrr,jyr,zdrq,xgrq,shrq,qrrq,
            shbs,qrbs,shgwid,pzzdr,pzzdbs,pzdqrq,pzkz,dycs,djzt,yyy,gbk,yskje,sskje,bc,
            lydjid,bz,yckdm,yid,spdlid,zzbs,fph,lydjlx,fkfs,zzh,scgzh) 
            select {3} tzid,'' dhbh,{1} djlx ,{2} djlb,1 djbs,@djh, CONVERT(VARCHAR(10), getdate(),120) rq ,1 khid,
            0 shdwid,{4} ckid,0 dfckid,0 je,0 cjje,0 skje,0 kpje,'{5}' zdr,'{5}' shr,'{5}' qrr,'' jyr, getdate() zdrq ,'' xgrq ,getdate() shrq ,getdate() qrrq,
            1 shbs, 1 qrbs,0 shgwid,'' pzzdr,0 pzzdbs,'' pzdqrq,'' pzkz,0 dycs,0 djzt,'' yyy,'' gbk,0 yskje,0 sskje,'' bc,
            a.id ,'pda入库' bz,'' yckdm,0 yid,'' spdlid,0 zzbs,a.fph,a.djlx,0 fkfs,a.zzh,a.scgzh
            from CL_T_dddjb a  where a.id={0};
            SET @id=SCOPE_IDENTITY();
            select @khid=khid  from  cl_t_kcdjb where id={0};
            ";
            sql = string.Format(sql, lydjid, djlx, zb_djlb, tzid, zb_ckid, username);
            string lydjlx = "";
            foreach (Dictionary<string, string> d in dataList)
            {
                if (lydjid == int.Parse(d["id"].ToString()))
                {
                    lydjlx = d["djlx"].ToString();
                    string mx_qwh = "";//区位号
                    sql += @"insert into cl_t_kcdjmx (ypgzh,lymxid,id,chdm,sl,dj,je,scddbh)  
                    select '{0}' ypgzh,{1} lymxid,@id,a.chdm,{2} sl,a.dj,{2}*a.dj,a.scddbh from CL_T_dddjmx a where a.mxid={1}
                    ";
                    sql = string.Format(sql, mx_qwh, d["mxid"].ToString(), d["smsl"].ToString());
                }
            }
            sql += @"
                update cl_t_kcdjb set mxjls=(select count(id) from cl_t_kcdjmx where id=@id) where id=@id;
                update cl_t_kcdjb set je=(select sum(je) from cl_t_kcdjmx where id=@id group by id) where id=@id;
        ";
            sql = string.Format(sql, lydjid, lydjlx);
        }
        foreach (TMRecode tm in tmRecodeList)
        {
            //如果重复保存,那么这后面的保存失败
            sql += @"

            delete a from cl_t_cltmb a inner join  cl_t_wltmb b on a.tm=b.tm where b.tm='{0}' and a.tzid={1} and a.zdr='20181222全导'  ;

            insert cl_t_cltmb(syzt,tzid,tmlb,chdm,ddh,tmdw,sl,tm,zdr,lymxid,lydjid,lydjlx,kwdm,bz,rq,mlbz,fs,gh,fk,kz,sxsl,zl)
             select 0, {1} tzid,a.tmlb,a.chdm,a.ddh,a.tmdw,a.sl,a.tm,'{2}' zdr,0 lymxid,0 lydjid,0 lydjlx,'{3}' kwdm,'pda入库',getdate(),a.mlbz,a.fs,a.gh,a.fk,a.kz,a.sxsl,a.zl from cl_t_wltmb a where a.tm='{0}';
            ";
            sql = string.Format(sql, tm.tm, tzid, username, cwtm);
            foreach (int mxid in tm.tmMxid.Keys)
            {
                sql += @"
                insert cl_T_pdakcdj(tzid,djlx,tm,chdm,sl,cw,lymxid,lydjlx,zdr,zdrq,bz)
                select {1}tzid,2560,a.tm,a.chdm,{2} sl,'{3}' cw,{4}lymxid,b.djlx lydjlx,'{5}' zdr,getdate(),'pda入库' from cl_t_wltmb a inner join cl_v_dddjmx b on b.mxid={4} where a.tm='{0}'
                ";
                sql = string.Format(sql, tm.tm, tzid, tm.tmMxid[mxid], cwtm, mxid, username);
            }
        }
        sql += @" 
commit transaction 
end try 
begin catch 
select ERROR_NUMBER() as errornumber,'数据库操作错误' errmsg 
rollback transaction 
end catch ;";
        //result.Data = sql;        
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            DataTable dt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            string errInfo = dal.ExecuteQuerySecurity(sql, para, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    if (dt.Rows[0]["errmsg"].ToString().IndexOf("数据库操作错误") > -1)
                    {
                        result.Errmsg = dt.Rows[0]["errmsg"].ToString();
                        result.Errcode = 100;
                    }
                }
                else
                {
                    result.Data = "保存成功";
                }
            }
            else
            {
                result.Errmsg = errInfo;
                result.Errcode = 100;
            }
            writeLog(sql);
        }
        return result;
    }


    [WebMethod(Description = "五里陈佳入库上架")]
    public Result getCLTMWL(string tmid, string tzid)
    {
        Result result = new Result();
        string errInfo = "";
        if (tzid == "")
        {
            result.Data = "tzid参数为空！";
            result.Errcode = 100;
        }
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                //tmid是运强发货打印的物料领用计划
                string str_sql = @" 
                   select a.tm,a.chdm,a.ddh,a.sl,a.rq,a.id,ch.chmc  from cl_t_cltmb a inner join cl_T_chdmb ch on ch.chdm=a.chdm and ch.tzid=1 where a.tzid=@tzid and a.tm=@tm
                   union all
                   SELECT b.tm,c.chdm,c.ddh,b.sl,c.rq,c.id,ch.chmc 
                   FROM dbo.cl_t_wlkb a 
                   INNER JOIN dbo.cl_t_wlkmxb b ON a.id=b.id
                   INNER JOIN dbo.cl_t_cltmb c ON b.tm=c.tm
                   INNER JOIN dbo.CL_T_chdmb ch ON c.chdm=ch.chdm
                   WHERE a.wlkbh=@tm AND c.tzid=@tzid
                ";

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@tzid", tzid));
                para.Add(new SqlParameter("@tm", tmid));
                errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "cl_t_cltmb";
                        result.Data = SerializeDataTableXml(dt);
                    }
                    else
                    {
                        result.Data = "查询无记录！";
                        result.Errcode = 101;
                    }
                }
                else
                {
                    result.Data = errInfo;
                    result.Errcode = 200;
                }
            }
        }
        return result;
    }

    [WebMethod(Description = "五里陈佳入库上架保存")]
    public Result saveCLTMWL(string username, string tzid, string data, string cwtm)
    {

        Result result = new Result();
        List<int> dataList = JsonConvert.DeserializeObject<List<int>>(data);
        string sql = @"";

        foreach (int djid in dataList)
        {
            sql += @"  
                update cl_t_cltmb set kwdm='{1}' where id={0}
            ";
            sql = string.Format(sql, djid, cwtm);

        }

        //result.Data = sql;
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            DataTable dt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            string errInfo = dal.ExecuteQuerySecurity(sql, para, out dt);
            if (errInfo == "")
            {
                result.Data = "保存成功";
            }
            else
            {
                result.Errmsg = errInfo;
                result.Errcode = 100;
            }
        }
        return result;
    }


    //条码生成模块的条码生成函数    
    [WebMethod(Description = "五里条码折分")]
    public Result GenerateTMWL(string zdr, string tzid, string txtTm, string txbsl, string txbbz)
    {
        Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            string str_sql = @"declare @strTm bigint ;
                /*生成总部的wltmb*/
                set @strTm=CAST(right(CONVERT(varchar(12), getdate(), 112),6) + isnull((select top 1 right(tm,5) as b from cl_t_wltmb where CONVERT(varchar(12),rq,112)=CONVERT(varchar(12),getdate(),112) and left(tm,1) not in ('8','9') order by id desc),'00000') AS BIGINT)+1;
                insert cl_t_wltmb(tzid,tmlb,chdm,bz,tmdw,rq,sl,tm,syzt,zdr,zl,fs,gh,mlbz,fk,kz,ddh,sxsl) 
                select @tzid,tmlb,chdm,@bz+',PDA扫'+tm+'条码生成,',tmdw,getdate(),@sl,@strTm,0,@zdr,zl,fs,gh,mlbz,fk,kz,ddh,sxsl from cl_t_cltmb where tzid=@tzid and tm=@tm;

                /*生成加工厂的条码表*/
                insert cl_t_cltmb(tzid,tmlb,chdm,bz,tmdw,rq,sl,tm,syzt,zdr,zl,fs,gh,mlbz,fk,kz,ddh,sxsl,kwdm,lydjid,lydjlx)
                select @tzid,tmlb,chdm,@bz+',PDA扫'+tm+'条码生成,',tmdw,getdate(),@sl,@strTm ,0,@zdr,zl,fs,gh,mlbz,fk,kz,ddh,sxsl,kwdm,1,1 from cl_t_cltmb where tzid=@tzid and tm=@tm;
                
                
                insert cl_t_cltmjlb(tmid,tzid,tm,ysl,sl,zdrq ,zdr,lytmid) 
                select b.id ,@tzid,@strTm,a.sl,@sl,getdate(),@zdr,a.id from cl_t_cltmb a inner join cl_t_cltmb b on b.tm=cast(@strTm as varchar(max))  where a.tzid=@tzid and a.tm=cast(@tm as varchar(max));

                update cl_t_cltmb set sl=sl-@sl where tzid=@tzid and tm=@tm;
                select @strTm ;";//SCOPE_IDENTITY();
            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@sl", txbsl));
            paras.Add(new SqlParameter("@zdr", zdr));
            paras.Add(new SqlParameter("@bz", txbbz));
            paras.Add(new SqlParameter("@tm", txtTm));
            paras.Add(new SqlParameter("@tzid", tzid));
            DataTable dt = null;
            result.Data = str_sql;
            string errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    result.Data = "生成成功" + dt.Rows[0][0].ToString();
                }
                else
                {
                    result.Data = "生成失败";
                    result.Errcode = 101;
                }
            }
            else
            {
                result.Data = "Error:" + errInfo;
                result.Errcode = 100;
            }
        }//end using

        return result;
    }

    //五里领料
    [WebMethod(Description = "加载主表数据 数据源是五里仓储备发领料")]
    public Result getWLLLlist(string ksrq, string jsrq, string tzid, string userid)
    {
        Result result = new Result();
        if (tzid == "")
        {
            result.Errcode = 100;
            result.Errmsg = "tzid参数为空！";
        }
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @"  
DECLARE @ryid VARCHAR(50);
 SELECT @ryid=ryid FROM t_user WHERE id={0}      ;  

SELECT max(c.mc) as cjmc,a.sphh+'_'+b.pc as ch,a.chdm,sum(b.jhyl) as jhyl,a.id
                    FROM sc_t_sccjb a 
                    inner join sc_t_sccjmx b on b.id=a.id 
                    left join sc_t_scbmb c on a.cjid=c.id                    
                    where a.tzid=@tzid  and a.rq>=@ksrq and a.rq<=dateadd(day,1,@jsrq) AND a.djlx=371 and b.bfsq=5 
                    and ','+b.ccblrid+',' like '%,'+@ryid+',%' 
                    group by a.sphh,b.pc,a.chdm,a.id                 
                ";

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@ksrq", ksrq));
                para.Add(new SqlParameter("@jsrq", jsrq));
                para.Add(new SqlParameter("@tzid", tzid));
                string errInfo = dal.ExecuteQuerySecurity(string.Format(str_sql, userid), para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "cl_t_wlll";
                        result.Data = SerializeDataTableXml(dt);
                    }
                    else
                    {
                        result.Data = "查询无记录";
                        result.Errcode = 101;
                    }
                }
                else
                {
                    result.Data = "查询出错：" + errInfo;
                    result.Errcode = 100;
                }
            }
        }

        return result;
    }

    //五里领料
    [WebMethod(Description = "加载明细数据 数据源是五里仓储备发领料")]
    public Result getWLLLlistMX(string str, string tzid, string userid)
    {
        Result result = new Result();
        if (tzid == "")
        {
            result.Errcode = 100;
            result.Errmsg = "tzid参数为空！";
        }
        else
        {
            List<WLLLData> obj = XmlDeSerialize<List<WLLLData>>(str);
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                List<String> detailList = new List<String>();
                foreach (WLLLData wll in obj)
                {
                    detailList.Add("select id=" + wll.id.ToString() + ",ch='" + wll.ch + "'" + (detailList.Count == 0 ? " into #xz " : ""));
                }
                string str_sql = string.Join("union ", detailList.ToArray());
                str_sql += @"  
 DECLARE @ryid VARCHAR(50);
 SELECT @ryid=ryid FROM t_user WHERE id={0}      ;              
                     
                    SELECT  a.chdm,sum(b.jhyl) as sl,0 smsl
                    FROM sc_t_sccjb a 
                    inner join sc_t_sccjmx b on b.id=a.id 
                    left join sc_t_scbmb c on a.cjid=c.id 
                    inner join #xz xz on xz.id=a.id and xz.ch=a.sphh+'_'+b.pc
                    where a.tzid=@tzid   AND a.djlx=371 and b.bfsq=5 
                    and ','+b.ccblrid+',' like '%,'+@ryid+',%' 
                    group by a.chdm
                ";

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@tzid", tzid));
                string errInfo = dal.ExecuteQuerySecurity(string.Format(str_sql, userid), para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "chdm";
                        result.Data = SerializeDataTableXml(dt);
                    }
                    else
                    {
                        result.Data = "查询无记录";
                        result.Errcode = 101;
                    }
                }
                else
                {
                    result.Data = "查询出错：" + errInfo;
                    result.Errcode = 100;
                }
            }
        }
        return result;
    }


    //五里领料
    [WebMethod(Description = "五里仓储备发领料 物料扫码 ")]
    public Result getWLLLlistMXScan(string tm, string tzid)
    {
        Result result = new Result();
        if (tzid == "")
        {
            result.Errcode = 100;
            result.Errmsg = "tzid参数为空！";
        }
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;

                string str_sql = @"  
                    SELECT  a.chdm,a.sl,isscaned=ISNULL((SELECT 1 FROM cl_T_pdakcdj a WHERE a.djlx=2570 AND a.tzid=@tzid AND a.lydjlx=371 AND a.tm=@tm),0)
                    FROM cl_t_cltmb a                  
                    where a.tzid=@tzid  and a.tm=@tm
                ";

                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@tzid", tzid));
                para.Add(new SqlParameter("@tm", tm));
                string errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        if (dt.Rows[0]["isscaned"].ToString() == "1")
                        {
                            result.Data = "查询出错：已刷码出库";
                            result.Errcode = 103;
                        }
                        else
                        {
                            dt.TableName = "chdm";
                            result.Data = SerializeDataTableXml(dt);
                        }
                    }
                    else
                    {
                        result.Data = "查询无记录";
                        result.Errcode = 101;
                    }
                }
                else
                {
                    result.Data = "查询出错：" + errInfo;
                    result.Errcode = 100;
                }
            }
        }
        return result;
    }


    [WebMethod(Description = "五里陈佳物料领用保存数据")]
    public Result saveDataWLLLWL(string username, string tzid, string tmRecode, string wlljl)
    {
        Result result = new Result();
        List<TMRecode> tmRecodeList = JsonConvert.DeserializeObject<List<TMRecode>>(tmRecode);
        List<WLLLData> wll = JsonConvert.DeserializeObject<List<WLLLData>>(wlljl);
        string guid = Guid.NewGuid().ToString();
        string sql = @"";
        string djlx = "2570";
        foreach (TMRecode tm in tmRecodeList)
        {
            sql += @"
              update cl_t_cltmb set bz=bz+',pda出库,操作员:{1}' where tzid=@tzid and  tm='{0}';
              IF NOT EXISTS (SELECT 1 FROM dbo.cl_T_pdakcdj WHERE djlx=2570 and tm='{0}' and guid='{2}')
              insert cl_T_pdakcdj(tzid,djlx,tm,chdm,sl,cw,lymxid,lydjlx,zdr,zdrq,bz,guid)
              select a.tzid,{3},a.tm,a.chdm,a.sl,kwdm,0 lymxid,371 lydjlx,'{1}' zdr,getdate(),'pda出库','{2}' from cl_t_cltmb a where  a.tzid=@tzid and  a.tm='{0}';
            ";
            sql = string.Format(sql, tm.tm, username, guid, djlx);
        }
        foreach (WLLLData w in wll)
        {
            sql += @" insert sc_T_sccjsmb (tzid,guid,djlx,lymxid,ch,bz) values(@tzid,'{0}',{1},{2},'{3}','pda出库')";
            sql = string.Format(sql, guid, djlx, w.id, w.ch);
        }
        //出库单，要放在这个位置
        sql += @" DECLARE @htddid INT;DECLARE @id INT ; DECLARE @gzh VARCHAR(500);DECLARE @cpjj VARCHAR(500);
 SELECT @htddid= MAX(e.htddid) ,@gzh=MIN(ht.gzh),@cpjj=MAX(ht.cpjj)
 FROM sc_T_sccjsmb c 
 INNER JOIN sc_T_sccjmx e ON c.lymxid=e.id
 INNER JOIN sc_T_sccjb f ON f.id=e.id
 INNER JOIN zw_v_cphtddmx ht ON ht.id=e.htddid AND ht.sphh=f.sphh
 WHERE c.djlx=2570 AND  c.guid='{0}'
 SELECT @htddid,@gzh

 INSERT cl_t_kcdjb(tzid,dhbh,djlx,djlb,djbs,djh,rq,khid,shdwid,je,skje,kpje,zdr,shr,qrr,zdrq,xgrq,shrq,qrrq,shbs,qrbs,
 shgwid,dycs,djzt,zzbs,zzrq,zzr,lydjid,bz,yid,fkfs,fph,lydjlx,ckid,zzh,scgzh,htddid,spdlid,pzzdbs,dfckid)
 SELECT @tzid tzid,@cpjj,511 djlx, 6833 djlb,1 djbs, isnull(max(b.djh)+1,'100001') djh,GETDATE(),125 khid,
 0 shdwid,0 je,0 skje,0 kpje,'{1}',''shr,''qrr,GETDATE() zdrq,''xgrq,''shrq,''qrrq,0 shbs,0 qrbs,
 shgwid=(select shgwid as value from xt_t_djshgw where tzid=@tzid and djlxid=511 and xh=1),
 0  dycs,0 djzt,0 zzbs,''zzrq,''zzr,0 lydjid,'PDA领用' bz,0 yid,'' fkfs,''fph,''lydjlx,8253 ckid,'{0}' zzh,'合同导单'scgzh,
 @htddid htddid,0 spdlid,0 pzzdbs,8253 ckid
  from  cl_T_kcdjb b 
 WHERE   b.tzid=@tzid  and year(b.rq)=year(getdate()) and month(b.rq)=month(getdate()) and b.djlx=511
 SET @id=SCOPE_IDENTITY();

 INSERT cl_t_kcdjmx (id,chdm,sl,js,zxid,zks,dj,je,lymxid,djzt,yid,shdm,scddbh)
 SELECT @id,a.chdm,a.sl,0 js,0 zxid,0 zks, ch.ccdj,ch.ccdj*a.sl AS je,0 ymxid,0 djzt,0 yid,'' shdm,@gzh
 FROM (
   SELECT a.chdm,SUM(a.sl) sl
   FROM cl_T_pdakcdj a 
   WHERE a.djlx=2570 AND  a.guid='{0}'
   GROUP BY chdm
 )a
 INNER JOIN dbo.CL_T_chdmb ch ON ch.chdm=a.chdm AND ch.tzid=1";
        sql = string.Format(sql, guid, username);
        //result.Data = sql;
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            DataTable dt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            string errInfo = dal.ExecuteQuerySecurity(sql, para, out dt);
            if (errInfo == "")
            {
                result.Data = "保存成功";
            }
            else
            {
                result.Errmsg = errInfo;
                result.Errcode = 100;
            }
        }
        return result;
    }
    //加载单据表明细 传入的是多张领用计划单
    [WebMethod(Description = "加载单据表明细 传入的是多张领用计划单")]
    public string getZXDMXList(string ids, string tzid)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @"select b.djh,b.id,a.mxid,a.chdm,d.chmc,a.sl,d.dw,a.scddbh,b.ckid into #zb
                                from cl_t_dddjb  b 
                                inner join cl_t_dddjmx a on a.id=b.id 
                                inner join cl_v_chdmb d on d.tzid={0} and d.chdm=a.chdm   
                                where b.id in ({1});

                                select a.chdm,'0' xlsl,isnull(a.sl,0)-isnull(b.sl,0) sysl,a.sl,isnull(b.sl,0) zxsl,a.mxid,a.id,a.chmc,a.dw,a.scddbh,'' tm,'' zxbz,'0' yssl,'0' sssl,a.ckid
                                from #zb a
                                left join (
                                select sum(a.sl) sl,a.lyid,a.lymxid from cl_t_wlzxd a inner join #zb b on a.lymxid=b.mxid group by a.lyid,a.lymxid 
                                ) b on a.id=b.lyid and a.mxid=b.lymxid order by a.chdm;
                                drop table #zb;";
                DataTable dt2 = null;
                str_sql = string.Format(str_sql, tzid, ids);
                errInfo = dal.ExecuteQuery(str_sql, out dt2);
                if (errInfo == "")
                {
                    if (dt2.Rows.Count > 0)
                    {
                        dt2.TableName = "cl_t_wlzxdmx";
                        rtMsg = SerializeDataTableXml(dt2);
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                }
            }
        }

        return rtMsg;
    }


    //检查箱码的有效性
    [WebMethod(Description = "检查箱码的有效性")]
    public string checkXM(string tzid, string tm, string ids)
    {
        string rtMsg = "", str_sql = "";
        if (tm == "")
            rtMsg = "error:箱码参数为空！";
        else if (tzid == "")
            rtMsg = "error:当前套账ID参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                str_sql = "select count(*) from cl_t_wlzxd where zxxh='" + tm + "'";
                DataTable dt = null;
                string errInfo = dal.ExecuteQuery(str_sql, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows[0][0].ToString() == "0")
                    {
                        //rtMsg = "error:该箱码无效！";
                        //不能直接判定为无效箱码，因为后面箱码有可能直接由物料条码直接生成
                        //20160509  李清峰调整
                        str_sql = @"if not exists (select top 1 1 from cl_t_wlsmjlb where zxxh='{0}' and smlb='tm' and zxbs=1)
                                      select '00';
                                    else if exists (select top 1 1 from cl_t_wlsmjlb where tzid=1 and smlb='xh' and sm='{0}')
                                      select '01',zxxh from cl_t_wlsmjlb where tzid=1 and smlb='xh' and sm='{0}';
                                    else
                                      begin
                                        select a.id
                                        from cl_t_wlsmjlb a
                                        inner join cl_t_wltmb tm on a.sm=tm.tm and a.zxxh='{0}' and a.smlb='tm' and a.zxbs=1
                                        left join cl_t_dddjmx s on s.chdm=tm.chdm and s.id in ({1})
                                        where s.chdm is null;
                                      end";
                        str_sql = string.Format(str_sql, tm, ids);
                        errInfo = dal.ExecuteQuery(str_sql, out dt);
                        if (errInfo == "")
                        {
                            if (dt.Rows.Count > 0)
                            {
                                string dm = Convert.ToString(dt.Rows[0][0]);
                                if (dm == "00")
                                    rtMsg = "error:该箱码无效！！";
                                else if (dm == "01")
                                    rtMsg = "error:该箱码已装箱，装箱号为：" + Convert.ToString(dt.Rows[0][1]);
                                else
                                    rtMsg = "error:该箱码与要装箱的材料不符！";
                            }
                            else
                            {
                                str_sql = @"select tm.chdm+':'+convert(char(10),sum(tm.sl))+'|'
                                            from cl_t_wlsmjlb a
                                            inner join cl_t_wltmb tm on a.sm=tm.tm 
                                            and a.zxxh='{0}' and a.smlb='tm' and a.zxbs=1
                                            group by tm.chdm
                                            for xml path('');";
                                str_sql = string.Format(str_sql, tm);
                                if (dt != null)
                                {
                                    dt.Dispose();
                                    dt = null;
                                }
                                errInfo = dal.ExecuteQuery(str_sql, out dt);
                                if (errInfo == "" && dt.Rows.Count > 0)
                                {
                                    rtMsg = "success:" + dt.Rows[0][0].ToString();
                                }
                                else
                                {
                                    rtMsg = "error:获取对应箱码装箱数据2失败：" + errInfo;
                                    writeLog(rtMsg + "\r\n" + str_sql);
                                }
                            }
                        }
                        else
                        {
                            rtMsg = "error:查询箱码2使用情况时出错:" + errInfo;
                            writeLog(rtMsg + "\r\n" + str_sql);
                        }
                    }
                    else
                    {
                        str_sql = " declare @zxh varchar(50);set @zxh=isnull((select isnull(zxxh,0) from cl_t_wlsmjlb where tzid='" + tzid + "' and smlb='xh' and sm = '" + tm + "'),0);";
                        str_sql += " if @zxh='0' select top 1 convert(char(10),count(*))+'|' from cl_t_dddjmx a inner join cl_t_wlzxd wl on wl.lymxid=a.mxid and wl.zxxh = '" + tm + "'";
                        str_sql += " left outer join cl_t_dddjmx l on l.chdm=a.chdm and l.id in (" + ids + ")  where l.chdm is null; else select @zxh;";
                        dt = null;
                        errInfo = dal.ExecuteQuery(str_sql, out dt);
                        if (errInfo == "" && dt.Rows.Count > 0)
                        {
                            string[] rt = dt.Rows[0][0].ToString().Split('|');
                            if (rt.Length == 1)
                                rtMsg = "error:该箱码已装箱，装箱号为：" + rt[0];
                            else
                            {
                                if (Convert.ToInt32(rt[0]) > 0)
                                    rtMsg = "error:该箱码与要装箱的材料不符！";
                                else
                                {
                                    str_sql = "select a.chdm+':'+convert(char(10),sum(wl.sl))+'|' from cl_t_dddjmx a inner join cl_t_wlzxd wl on wl.lymxid=a.mxid and wl.zxxh = '" + tm + "' group by a.chdm for xml path('');";
                                    dt = null;
                                    errInfo = dal.ExecuteQuery(str_sql, out dt);
                                    if (errInfo == "" && dt.Rows.Count > 0)
                                    {
                                        rtMsg = "success:" + dt.Rows[0][0].ToString();
                                    }
                                    else
                                    {
                                        rtMsg = "error:获取对应箱码装箱数据失败：" + errInfo;
                                        writeLog(rtMsg + "\r\n" + str_sql);
                                    }
                                }//提取该箱码中对应的所有材料及数量                              
                            }
                        }
                        else
                        {
                            rtMsg = "error:查询箱码使用情况时出错:" + errInfo;
                            writeLog(rtMsg + "\r\n" + str_sql);
                        }
                    }
                }
                else
                {
                    rtMsg = "error:查询箱码是否有效时出错：" + errInfo;
                    writeLog(rtMsg + "\r\n" + str_sql);
                }
            }
        }

        return rtMsg;
    }

    [WebMethod]
    public string checkTM(string tzid, string tm)
    {
        string rtMsg = "", str_sql = "";
        if (tm == "")
            rtMsg = "error:条码参数为空！";
        else if (tzid == "")
            rtMsg = "error:当前套账ID参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                str_sql = @"declare @zxxh varchar(50);declare @str varchar(100); 
                        select @zxxh=isnull((select top 1 isnull(a.zxxh,0) from cl_t_wlsmjlb a where a.tzid=@tzid and a.smlb='tm' and a.sm=@tm),0);

                        if @zxxh = '0' 
                        begin 
                        select @str=isnull((select convert(char(8),a.id)+'|'+isnull(a.chdm,'')+'|'+ convert(char(10),isnull(a.sl,0)) from cl_t_wltmb a where a.tm=@tm),-1) end
                        else 
                        begin 
                        select @str=@zxxh 
                        end; 

                        select @str;";
                DataTable dt = null;
                List<SqlParameter> para = new List<SqlParameter>();
                para.Add(new SqlParameter("@tzid", tzid));
                para.Add(new SqlParameter("@tm", tm));

                string errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        string result = dt.Rows[0][0].ToString();
                        if (result == "-1")
                            rtMsg = "warn:扫描的条码不存在！";
                        else if (result != "")
                            rtMsg = "success:" + result;
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                }
            }
        }
        return rtMsg;
    }

    [WebMethod(Description = "保存数据2")]
    public string saveData2String(string objStr, string username, string tzid, string tmArrayStr, string xmArrayStr, string tmDict, string xzry, string fcfa)
    {
        string[] tmArray;
        string[] xmArray;

        if (!string.IsNullOrEmpty(tmArrayStr)){
            tmArray = tmArrayStr.Split(',');
        } else {
            tmArray = new string[0];
        }

        if (!string.IsNullOrEmpty(xmArrayStr)){
            xmArray = xmArrayStr.Split(',');
        } else {
            xmArray = new string[0];
        }
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "saveData2String-tmArray:"+tmArray+" length:"+tmArray.Length);
        Dictionary<string, List<Dictionary<string, string>>> tmdata = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(tmDict);
        List<Dictionary<string, string>> tmll = new List<Dictionary<string, string>>();

        Result rst = new Result();
        DataTable tmdt = new DataTable();
        DataColumn dc = null;
        dc = tmdt.Columns.Add("gzh", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("chdm", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("ckid", Type.GetType("System.Int32"));
        dc = tmdt.Columns.Add("tzid", Type.GetType("System.Int32"));
        dc = tmdt.Columns.Add("tm", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("sl", Type.GetType("System.Single"));
        dc = tmdt.Columns.Add("cw", Type.GetType("System.String"));

        string rtMsg = "";
        //string sql = " ";
        StringBuilder sb = new StringBuilder();
        DataTable tmdtcw = new DataTable();
        DataTable xmdtcw = new DataTable();
        if (username == "")
            rtMsg = "error:当前用户名为空！";
        else if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {

            string uuid = System.Guid.NewGuid().ToString("N");
            ZXDData obj = XmlDeSerialize<ZXDData>(objStr);
            string str_sql = " SET XACT_ABORT ON ;BEGIN TRAN ";
            str_sql += " declare @fcbl decimal(9,3); select distinct items ryid into #xzry from dbo.f_Split('" + xzry + "',','); ";
            str_sql += " select @fcbl= case @fcfa when 1 then CAST(ROUND(1.0/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) ELSE CAST(ROUND(0.9/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) END; ";
            str_sql += " declare @zxid int;declare @zxh varchar(50);declare @zpc varchar(50);declare @id int; ";
            str_sql += " select @zpc=right(convert(varchar(8),getdate(),112),6)+'_" + obj.khdm + "_" + obj.pc + "';";
            str_sql += " declare @maxdjh varchar(6);set @maxdjh='001';";
            str_sql += " select top 1  @maxdjh=right('000'+cast(cast(right(a.zxxh,3) as int)+1 as varchar),3) from cl_t_wlzxd a ";
            str_sql += " where a.tzid=1 and a.zxpc=@zpc order by a.zxxh desc;set @zxh=@zpc+'_'+@maxdjh;";
            #region
            if (obj.mxTable.Rows.Count > 0)
            {
                string isxj = "0";
                isxj = tzid == "1" ? "0" : "1";
                List<string> zxList = new List<string>();
                for (int i = 0; i < obj.mxTable.Rows.Count; i++)
                {
                    if (obj.mxTable.Rows[i]["xlsl"].ToString() == "")
                        continue;
                    else if (Convert.ToDouble(obj.mxTable.Rows[i]["xlsl"]) == 0)
                        continue;
                    //str_sql += " insert into cl_t_wlzxd(tzid,zxxh,lyid,lymxid,sl,zdrq,zdr,bz,zxpc,xzl,zbz,isxj) values ";
                    zxList.Add(" select '" + uuid + "',@tzid,@zxh,'" + obj.mxTable.Rows[i]["id"].ToString() + "','" + obj.mxTable.Rows[i]["mxid"].ToString() + "','" + obj.mxTable.Rows[i]["xlsl"].ToString() + "',getdate(),@username,'" + obj.mxTable.Rows[i]["zxbz"].ToString() + "',@zpc,'" + obj.xzl + "','" + obj.bz + "','" + isxj + "' ");
                    //str_sql += " (@tzid,@zxh,'" + obj.mxTable.Rows[i]["id"].ToString() + "','" + obj.mxTable.Rows[i]["mxid"].ToString() + "','" + obj.mxTable.Rows[i]["xlsl"].ToString() + "'";
                    //str_sql += ",getdate(),@username,'" + obj.mxTable.Rows[i]["zxbz"].ToString() + "',@zpc,'" + obj.xzl + "','" + obj.bz + "','" + isxj + "');";
                }
                str_sql += " insert into cl_t_wlzxd(uuid,tzid,zxxh,lyid,lymxid,sl,zdrq,zdr,bz,zxpc,xzl,zbz,isxj) " + string.Join(" union all ", zxList.ToArray());
                str_sql += " insert into cl_T_pdaxzry ( uuid, ryid, fcbl ) select '" + uuid + "' ,ryid,@fcbl from #xzry ";
                //处理条码               
                if (tmArray.Length > 0)
                {
                    str_sql += "select a.sm into #tmtmp from (";
                    for (int i = 0; i < tmArray.Length; i++)
                    {
                        tmll = tmdata[tmArray[i]];
                        foreach (Dictionary<string, string> ff in tmll)
                        {
                            DataRow dr = tmdt.NewRow();
                            dr["gzh"] = ff["scddbh"].ToString();
                            dr["chdm"] = ff["chdm"].ToString();
                            dr["ckid"] = ff["ckid"].ToString();
                            dr["sl"] = ff["sl"].ToString();
                            dr["tzid"] = tzid;
                            dr["tm"] = tmArray[i];
                            tmdt.Rows.Add(dr);
                        }
                        if (i == tmArray.Length - 1)
                            str_sql += "select '" + tmArray[i] + "' sm) a ";
                        else
                            str_sql += "select '" + tmArray[i] + "' sm union all ";
                    }
                    rst = getPDAkc(tmdt);
                    if (rst.Errcode > 0)
                        return rst.Errmsg;
                    else
                    {

                        //tmdtcw = DeserializeDataTable(rst.Data.ToString());
                        tmdtcw = (DataTable)rst.Data;

                        //foreach (DataRow dr in tmdtcw.Select())
                        //{
                        //    sql += " insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )";
                        //    sql += " values (2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',getdate(),'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "')";
                        //    sql += " SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ";
                        //}
                        //sql += "set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ";
                    }
                    str_sql += " update a set a.zxxh=@zxh,a.syzt=1 from cl_t_wltmb a inner join #tmtmp b on a.tm=b.sm;";
                    str_sql += " insert cl_t_wlsmjlb(tzid,sm,zxxh,smlb) select @tzid,a.sm,@zxh,'tm' from #tmtmp a ;drop table #tmtmp;";
                }
                //处理箱码
                nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "处理箱码前的sql："+str_sql);
                nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "xmArray的长度："+xmArray.Length);
                if (xmArray.Length > 0)
                {
                    for (int i = 0; i < xmArray.Length; i++)
                    {
                        tmll = tmdata[xmArray[i]];
                        str_sql += " insert into cl_t_wlsmjlb(tzid,sm,zxxh,smlb) values (@tzid,'" + xmArray[i] + "',@zxh,'xh');";
                        foreach (Dictionary<string, string> ff in tmll)
                        {
                            DataRow dr = tmdt.NewRow();
                            dr["gzh"] = ff["scddbh"].ToString();
                            dr["chdm"] = ff["chdm"].ToString();
                            dr["ckid"] = ff["ckid"].ToString();
                            dr["sl"] = ff["sl"].ToString();
                            dr["tzid"] = tzid;
                            dr["tm"] = xmArray[i];
                            tmdt.Rows.Add(dr);
                        }
                    }
                    rst = getPDAkc(tmdt);
                    if (rst.Errcode > 0)
                        return rst.Errmsg;
                    else
                    {
                        xmdtcw = (DataTable)rst.Data;
                        //StringBuilder tmdtcwSb = new StringBuilder();
                        //foreach (DataRow dr in xmdtcw.Select())
                        //{
                        //    tmdtcwSb.Append( " insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )");
                        //    tmdtcwSb.Append( " values (2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',getdate(),'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "')");
                        //    tmdtcwSb.Append( " SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ");
                        //}
                        //writeLog( "cc:" +DateTime.Now.ToString());
                        //sql += "set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ";
                    }
                }

                tmdtcw.Merge(xmdtcw);

                //StringBuilder tmdtcwSb = new StringBuilder();
                List<string> tmdtcwList = new List<string>();
                foreach (DataRow dr in tmdtcw.Rows)
                {
                    //tmdtcwSb.Append(" insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )");

                    tmdtcwList.Add(" select 2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',@getdate,'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "'");
                    //tmdtcwSb.Append(" SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ");
                }

                //tmdtcwSb.Append("set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ");
                str_sql += " declare @getdate datetime;set @getdate=getdate(); declare @ids varchar(max);" +
                        "insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )" + string.Join(" union all ", tmdtcwList.ToArray()) +
                        "set @ids=(select CAST(id AS VARCHAR(max))+','  from cl_t_pdakcdj where djlx=2462 and zdr='" + username + "' and zdrq=@getdate FOR XML PATH('') ); exec pda_kc_kchz @ids,'sl-',0;  ";

                str_sql += "select @zxh;COMMIT TRAN GO;";
                nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "最后的sql："+str_sql);
                using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
                {
                    dal.ConnectionString = connStr;
                    DataTable dt = null;
                    List<SqlParameter> para = new List<SqlParameter>();
                    para.Add(new SqlParameter("@tzid", tzid));
                    para.Add(new SqlParameter("@username", username));
                    para.Add(new SqlParameter("@fcfa", fcfa));

                    string errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                    if (errInfo == "")
                    {
                        if (dt.Rows.Count > 0)
                        {
                            rtMsg = dt.Rows[0][0].ToString();
                        }
                    }
                    else
                    {
                        rtMsg = "error:" + errInfo;

                    }
                }
            }
            #endregion
            writeLog("\r\nSQL:" + str_sql + "\r\ntmList:" + string.Join(",", tmArray) + "\r\nxmList:" + string.Join(",", xmArray));
        }
        return rtMsg;
    }

    [WebMethod(Description = "保存数据")]
    public string saveData(string objStr, string username, string tzid, string[] tmArray, string[] xmArray, string tmDict, string xzry, string fcfa)
    {
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "saveData-xmArray:"+xmArray+" length:"+xmArray.Length);
        Dictionary<string, List<Dictionary<string, string>>> tmdata = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(tmDict);
        List<Dictionary<string, string>> tmll = new List<Dictionary<string, string>>();

        Result rst = new Result();
        DataTable tmdt = new DataTable();
        DataColumn dc = null;
        dc = tmdt.Columns.Add("gzh", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("chdm", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("ckid", Type.GetType("System.Int32"));
        dc = tmdt.Columns.Add("tzid", Type.GetType("System.Int32"));
        dc = tmdt.Columns.Add("tm", Type.GetType("System.String"));
        dc = tmdt.Columns.Add("sl", Type.GetType("System.Single"));
        dc = tmdt.Columns.Add("cw", Type.GetType("System.String"));

        string rtMsg = "";
        //string sql = " ";
        StringBuilder sb = new StringBuilder();
        DataTable tmdtcw = new DataTable();
        DataTable xmdtcw = new DataTable();
        if (username == "")
            rtMsg = "error:当前用户名为空！";
        else if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {

            string uuid = System.Guid.NewGuid().ToString("N");
            ZXDData obj = XmlDeSerialize<ZXDData>(objStr);
            string str_sql = " SET XACT_ABORT ON ;BEGIN TRAN ";
            str_sql += " declare @fcbl decimal(9,3); select distinct items ryid into #xzry from dbo.f_Split('" + xzry + "',','); ";
            str_sql += " select @fcbl= case @fcfa when 1 then CAST(ROUND(1.0/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) ELSE CAST(ROUND(0.9/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) END; ";
            str_sql += " declare @zxid int;declare @zxh varchar(50);declare @zpc varchar(50);declare @id int; ";
            str_sql += " select @zpc=right(convert(varchar(8),getdate(),112),6)+'_" + obj.khdm + "_" + obj.pc + "';";
            str_sql += " declare @maxdjh varchar(6);set @maxdjh='001';";
            str_sql += " select top 1  @maxdjh=right('000'+cast(cast(right(a.zxxh,3) as int)+1 as varchar),3) from cl_t_wlzxd a ";
            str_sql += " where a.tzid=1 and a.zxpc=@zpc order by a.zxxh desc;set @zxh=@zpc+'_'+@maxdjh;";
            #region
            if (obj.mxTable.Rows.Count > 0)
            {
                string isxj = "0";
                isxj = tzid == "1" ? "0" : "1";
                List<string> zxList = new List<string>();
                for (int i = 0; i < obj.mxTable.Rows.Count; i++)
                {
                    if (obj.mxTable.Rows[i]["xlsl"].ToString() == "")
                        continue;
                    else if (Convert.ToDouble(obj.mxTable.Rows[i]["xlsl"]) == 0)
                        continue;
                    //str_sql += " insert into cl_t_wlzxd(tzid,zxxh,lyid,lymxid,sl,zdrq,zdr,bz,zxpc,xzl,zbz,isxj) values ";
                    zxList.Add(" select '" + uuid + "',@tzid,@zxh,'" + obj.mxTable.Rows[i]["id"].ToString() + "','" + obj.mxTable.Rows[i]["mxid"].ToString() + "','" + obj.mxTable.Rows[i]["xlsl"].ToString() + "',getdate(),@username,'" + obj.mxTable.Rows[i]["zxbz"].ToString() + "',@zpc,'" + obj.xzl + "','" + obj.bz + "','" + isxj + "' ");
                    //str_sql += " (@tzid,@zxh,'" + obj.mxTable.Rows[i]["id"].ToString() + "','" + obj.mxTable.Rows[i]["mxid"].ToString() + "','" + obj.mxTable.Rows[i]["xlsl"].ToString() + "'";
                    //str_sql += ",getdate(),@username,'" + obj.mxTable.Rows[i]["zxbz"].ToString() + "',@zpc,'" + obj.xzl + "','" + obj.bz + "','" + isxj + "');";
                }
                str_sql += " insert into cl_t_wlzxd(uuid,tzid,zxxh,lyid,lymxid,sl,zdrq,zdr,bz,zxpc,xzl,zbz,isxj) " + string.Join(" union all ", zxList.ToArray());
                str_sql += " insert into cl_T_pdaxzry ( uuid, ryid, fcbl ) select '" + uuid + "' ,ryid,@fcbl from #xzry ";
                //处理条码               
                if (tmArray.Length > 0)
                {
                    str_sql += "select a.sm into #tmtmp from (";
                    for (int i = 0; i < tmArray.Length; i++)
                    {
                        tmll = tmdata[tmArray[i]];
                        foreach (Dictionary<string, string> ff in tmll)
                        {
                            DataRow dr = tmdt.NewRow();
                            dr["gzh"] = ff["scddbh"].ToString();
                            dr["chdm"] = ff["chdm"].ToString();
                            dr["ckid"] = ff["ckid"].ToString();
                            dr["sl"] = ff["sl"].ToString();
                            dr["tzid"] = tzid;
                            dr["tm"] = tmArray[i];
                            tmdt.Rows.Add(dr);
                        }
                        if (i == tmArray.Length - 1)
                            str_sql += "select '" + tmArray[i] + "' sm) a ";
                        else
                            str_sql += "select '" + tmArray[i] + "' sm union all ";
                    }
                    rst = getPDAkc(tmdt);
                    if (rst.Errcode > 0)
                        return rst.Errmsg;
                    else
                    {

                        //tmdtcw = DeserializeDataTable(rst.Data.ToString());
                        tmdtcw = (DataTable)rst.Data;

                        //foreach (DataRow dr in tmdtcw.Select())
                        //{
                        //    sql += " insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )";
                        //    sql += " values (2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',getdate(),'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "')";
                        //    sql += " SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ";
                        //}
                        //sql += "set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ";
                    }
                    str_sql += " update a set a.zxxh=@zxh,a.syzt=1 from cl_t_wltmb a inner join #tmtmp b on a.tm=b.sm;";
                    str_sql += " insert cl_t_wlsmjlb(tzid,sm,zxxh,smlb) select @tzid,a.sm,@zxh,'tm' from #tmtmp a ;drop table #tmtmp;";
                }
                //处理箱码
                if (xmArray.Length > 0)
                {
                    for (int i = 0; i < xmArray.Length; i++)
                    {
                        tmll = tmdata[xmArray[i]];
                        str_sql += " insert into cl_t_wlsmjlb(tzid,sm,zxxh,smlb) values (@tzid,'" + xmArray[i] + "',@zxh,'xh');";
                        foreach (Dictionary<string, string> ff in tmll)
                        {
                            DataRow dr = tmdt.NewRow();
                            dr["gzh"] = ff["scddbh"].ToString();
                            dr["chdm"] = ff["chdm"].ToString();
                            dr["ckid"] = ff["ckid"].ToString();
                            dr["sl"] = ff["sl"].ToString();
                            dr["tzid"] = tzid;
                            dr["tm"] = xmArray[i];
                            tmdt.Rows.Add(dr);
                        }
                    }
                    rst = getPDAkc(tmdt);
                    if (rst.Errcode > 0)
                        return rst.Errmsg;
                    else
                    {
                        xmdtcw = (DataTable)rst.Data;
                        //StringBuilder tmdtcwSb = new StringBuilder();
                        //foreach (DataRow dr in xmdtcw.Select())
                        //{
                        //    tmdtcwSb.Append( " insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )");
                        //    tmdtcwSb.Append( " values (2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',getdate(),'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "')");
                        //    tmdtcwSb.Append( " SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ");
                        //}
                        //writeLog( "cc:" +DateTime.Now.ToString());
                        //sql += "set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ";
                    }
                }

                tmdtcw.Merge(xmdtcw);

                //StringBuilder tmdtcwSb = new StringBuilder();
                List<string> tmdtcwList = new List<string>();
                foreach (DataRow dr in tmdtcw.Rows)
                {
                    //tmdtcwSb.Append(" insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )");

                    tmdtcwList.Add(" select 2462,'" + dr["tm"].ToString() + "','" + dr["gzh"].ToString() + "','" + dr["sl"].ToString() + "'," + tzid + ",'" + username + "',@getdate,'" + dr["chdm"].ToString() + "',1,1,1,getdate(),'" + dr["ckid"].ToString() + "','" + dr["cw"].ToString() + "'");
                    //tmdtcwSb.Append(" SET @id=SCOPE_IDENTITY(); set @ids=@ids+','+cast(@id as varchar); ");
                }

                //tmdtcwSb.Append("set @ids=SUBSTRING(@ids,2,LEN(@ids)); exec pda_kc_kchz @ids,'sl-',0; ");
                str_sql += " declare @getdate datetime;set @getdate=getdate(); declare @ids varchar(max);" +
                        "insert into cl_t_pdakcdj (djlx, tm, gzh, sl, tzid, zdr, zdrq, chdm, shbs, qrbs, djbs, rq, ckid, cw )" + string.Join(" union all ", tmdtcwList.ToArray()) +
                        "set @ids=(select CAST(id AS VARCHAR(max))+','  from cl_t_pdakcdj where djlx=2462 and zdr='" + username + "' and zdrq=@getdate FOR XML PATH('') ); exec pda_kc_kchz @ids,'sl-',0;  ";

                str_sql += "select @zxh;COMMIT TRAN GO;";
                using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
                {
                    dal.ConnectionString = connStr;
                    DataTable dt = null;
                    List<SqlParameter> para = new List<SqlParameter>();
                    para.Add(new SqlParameter("@tzid", tzid));
                    para.Add(new SqlParameter("@username", username));
                    para.Add(new SqlParameter("@fcfa", fcfa));

                    string errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                    if (errInfo == "")
                    {
                        if (dt.Rows.Count > 0)
                        {
                            rtMsg = dt.Rows[0][0].ToString();
                        }
                    }
                    else
                    {
                        rtMsg = "error:" + errInfo;

                    }
                }
            }
            #endregion
            writeLog("\r\nSQL:" + str_sql + "\r\ntmList:" + string.Join(",", tmArray) + "\r\nxmList:" + string.Join(",", xmArray));
        }
        return rtMsg;
    }

    //获取'物料领用计划单'对应的所有款号
    [WebMethod(Description = "获取'物料领用计划单'对应的所有款号")]
    public string GetLyJhDKhInfo(string tzid, string id)
    {
        string rtMsg = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                
                dal.ConnectionString = DBConnStr;
                dal.ConnectionString = connStr;
                string str_sql = @"					
					SELECT (SELECT DISTINCT d.spkh+','
					FROM cl_v_dddjmx a
					INNER JOIN cl_v_dddjmx b ON a.lymxid=b.mxid AND b.djlx=605
					INNER JOIN YX_T_Spcgjhb c ON b.scddbh=c.cggzh
					INNER JOIN YX_T_Spdmb d ON c.sphh=d.sphh
					WHERE a.id=@id
					FOR XML PATH('')) khs
				";
                List<SqlParameter> paras = new List<SqlParameter>();
                paras.Add(new SqlParameter("@id", id));
                DataTable dt = null;
                string errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        rtMsg = dt.Rows[0]["khs"].ToString();
                        if (rtMsg != "") rtMsg = rtMsg.Substring(0, rtMsg.Length - 1);
                    }
                    else
                        rtMsg = "Error:查询不到该'物料领用计划单'对应的款号！";
                }
                else
                {
                    rtMsg = "Error:查询'物料领用计划单'款号信息时出错！" + errInfo;
                }
            }
        }
        return rtMsg;
    }

    //通过'物料领用计划单'生成条码
    [WebMethod(Description = "通过'物料领用计划单'生成条码")]
    public string GetLyJhDInfo(string tzid, string zdr, string ids, string chdm, string djlx)
    {
        string rtMsg = "";
        string str_sql = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else if (ids == "")
            rtMsg = "error:没有刷领用单！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                //物料领用计划单
                if (djlx == "lyd")
                {
                    str_sql = @"					
			        SELECT c.id,d.djh,c.chdm,ch.chmc,sp.spkh SKU,c.sl,ch.dw as dadw,ch.fk,ys.mc ys
			        INTO #lyInfo
			        FROM cl_t_dddjb a
			        INNER JOIN cl_t_dddjmx b ON a.id=b.id
			        INNER JOIN cl_t_dddjmx c ON b.lymxid=c.mxid
			        INNER JOIN cl_t_dddjb d ON c.id=d.id
			        LEFT join yx_T_spcgjhb cg on cg.cggzh=c.scddbh
			        LEFT join YX_T_Spdmb sp on sp.sphh=cg.sphh
			        INNER JOIN CL_T_chdmb ch ON c.chdm=ch.chdm 
                    left join YX_T_Shdmb ys on ch.ysid=ys.id
			        WHERE a.id IN ({0}) AND c.chdm='{1}'
			   
			        SELECT a.id,a.djh,a.chdm,a.chmc,SUM(a.sl) sl,
				        (SELECT DISTINCT a1.SKU+'('+CAST(SUM(a1.sl) AS VARCHAR(50))+'),' FROM #lyInfo a1 WHERE a1.id=a.id GROUP BY a1.id,a1.chdm,a1.SKU FOR XML PATH('')) SKUS,
				        '' tm,'' dw,max(a.dadw) dadw,'' mlbz,max(a.fk) fk,max(a.ys) ys
			        FROM #lyInfo a
			        GROUP BY a.id,a.djh,a.chdm,a.chmc                          
			                                
			        DROP TABLE #lyInfo;  
		        ";
                }
                else if (djlx == "tzd")
                {
                    //物料到货通知单
                    str_sql = @" 
                        SELECT * INTO #ids FROM dbo.f_Split('{0}',',') WHERE LEFT(items,1)<>'S'
                        SELECT SUBSTRING(items,2,LEN(items)) id INTO #sid FROM  dbo.f_Split('{0}',',') WHERE LEFT(items,1)='S'

                        DECLARE @djids VARCHAR(500);
                        SET @djids=(SELECT SUBSTRING(a.ids,2,LEN(a.ids))+','  FROM cl_t_cwdjtmIDgl a 
                        INNER JOIN #sid b ON a.id=b.id 
                        FOR XML PATH('') )

                        select a.* into #myzb from cl_v_dddjmx a where a.id in (SELECT value FROM dbo.SplitToTable(@djids,','))  

                        SELECT * INTO #aa FROM (
                        SELECT a.id,a.djh,a.chdm,ch.chmc,a.scddbh sku,a.sl,ch.dw dadw,ch.fk,ys.mc ys
                        FROM dbo.cl_v_dddjmx a 
                        INNER JOIN #ids b ON (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))=b.items
                        INNER JOIN CL_T_chdmb ch ON a.chdm=ch.chdm
                        left join YX_T_Shdmb ys on ch.ysid=ys.id
                        WHERE a.djlx=624 AND a.chdm='{1}'
                        UNION 
                        select a.id,a.djh,a.chdm,ch.chmc,a.scddbh sku,a.sl,ch.dw dadw,ch.fk,ys.mc ys
                        from #myzb a 
                        INNER JOIN CL_T_chdmb ch ON a.chdm=ch.chdm
                        left join YX_T_Shdmb ys on ch.ysid=ys.id
                        where a.djlx=624 AND a.chdm='{1}'
                        ) a

                        SELECT a.id,a.djh,a.chdm,a.chmc,SUM(a.sl) sl,a.sku+'('+CAST(SUM(a.sl) AS VARCHAR(50))+')' skus,
                        '' tm,'' dw,MAX(a.dadw) dadw,'' mlbz,max(a.fk) fk,max(a.ys) ys
                        FROM #aa a
                        GROUP BY a.id,a.djh,a.chdm,a.chmc,a.sku

                        DROP TABLE #aa;DROP TABLE #ids;DROP TABLE #sid;drop table #myzb;
                    ";

                }
                List<SqlParameter> paras = new List<SqlParameter>();
                //paras.Add(new SqlParameter("@id", id));
                str_sql = string.Format(str_sql, ids, chdm);
                DataTable dt = null;
                string errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
                if (errInfo != "")
                {
                    rtMsg = "error:查询'物料领用计划单'信息！" + errInfo;
                    return rtMsg;
                }
                if (dt.Rows.Count == 0)
                {
                    rtMsg = "error:查询不到该'物料领用计划单'信息！";
                    return rtMsg;
                }
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (dt.Rows[i]["SKUS"].ToString().Length > 0)
                    {
                        dt.Rows[i]["SKUS"] = dt.Rows[i]["SKUS"].ToString().Substring(0, dt.Rows[i]["SKUS"].ToString().Length - 1);
                        if (dt.Rows[i]["SKUS"].ToString().Split(',').Length == 1)
                            dt.Rows[i]["SKUS"] = dt.Rows[i]["SKUS"].ToString().Split('(')[0];
                    }
                    rtMsg = SetLydTMSc(chdm, double.Parse(dt.Rows[i]["sl"].ToString()), dt.Rows[i]["SKUS"].ToString(), dt.Rows[i]["djh"].ToString(), zdr);
                    if (rtMsg.IndexOf("error") > -1)
                    {
                        return rtMsg;
                    }
                    dt.Rows[i]["tm"] = rtMsg.Split('|')[0];
                    dt.Rows[i]["dw"] = rtMsg.Split('|')[1];
                }
                dt.TableName = "cl_v_dddjmx";
                rtMsg = SerializeDataTableXml(dt);
            }
        }
        return rtMsg;
    }

    //条码生成
    public string SetLydTMSc(string chdm, double sl, string kh, string bz, string zdr)
    {
        string rtMsg = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            //string DBConnStr = "server='192.168.35.10';uid=ABEASD14AD;pwd=********;database=tlsoft";
            //dal.ConnectionString = DBConnStr;
            dal.ConnectionString = connStr;
            string str_sql = @"
                declare @strTm varchar(20),@dw varchar(20); 
                set @strTm=right(CONVERT(varchar(12), getdate(), 112),6) + isnull((select top 1 right(tm,5) as b from cl_t_wltmb where CONVERT(varchar(12),rq,112)=CONVERT(varchar(12),getdate(),112) and left(tm,1) not in ('8','9') order by right(tm,5) desc),'00000'); 
                SELECT TOP 1 @dw=dw FROM CL_T_chdmb WHERE chdm=@chdm ORDER BY id desc
                INSERT cl_t_wltmb(tzid,tmlb,chdm,bz,tmdw,rq,sl,tm,syzt,zdr,ddh) 
                SELECT TOP 1 1, CASE WHEN  SUBSTRING(chdm,1,1) IN ('a','b') THEN 2 ELSE 1 END  as tmlb ,chdm,@bz bz,dw,GETDATE(),@sl sl,cast(@strTm as bigint)+1,0,@zdr zdr,@kh ddh
                FROM CL_T_chdmb 
                WHERE chdm=@chdm
                ORDER BY id desc
                select cast(@strTm as bigint)+1,@dw;  
            ";
            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@sl", sl));
            paras.Add(new SqlParameter("@zdr", zdr));
            paras.Add(new SqlParameter("@kh", kh));
            paras.Add(new SqlParameter("@bz", bz));
            paras.Add(new SqlParameter("@chdm", chdm));
            DataTable dt = null;
            string errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    rtMsg = dt.Rows[0][0].ToString() + "|" + dt.Rows[0][1].ToString();
                }
                else
                    rtMsg = "error:生成失败！newid=0";
            }
            else
                rtMsg = "error:" + errInfo;
        }//end using

        return rtMsg;
    }

    //条码生成模块的条码信息查询
    [WebMethod(Description = "条码生成模块的条码信息查询")]
    public String GetTMInfo(string tmcode, string tzid)
    {
        string errInfo = "";
        Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;


            string str_sql = @"
                DECLARE @id INT  
                SELECT @id=a.id FROM  cl_t_wltmb a WHERE a.tm=@tmcode or a.chdm=@tmcode

                select top 1 a.id,a.chdm,isnull(a.bz,'') bz,CONVERT(varchar, a.rq, 102) rq ,b.chmc,
                    b.dw dadw,b.fk dafk,b.kez dakez,a.fs,a.gh,a.zl,
                    a.mlbz,a.tmdw,a.tmlb,a.fk,a.kz,
                    a.ddh,a.sl,b.tp datp,xj.mc  daxjchlbmc,ys.mc ys
                from cl_t_wltmb a 
                inner join CL_T_chdmb b on a.chdm=b.chdm and b.tzid=@zbid               
                left outer join cl_v_chlb as xj on b.xjchlbid=xj.id
                left join YX_T_Shdmb ys on b.ysid=ys.id
                where a.id=@id
            ";

            string sql = @"
                DECLARE @id INT                  
                if exists (SELECT a.id FROM  cl_t_cltmb a WHERE a.tm=@tmcode or a.chdm=@tmcode)
                begin 
                SELECT @id=a.id FROM  cl_t_cltmb a WHERE a.tm=@tmcode or a.chdm=@tmcode
                select top 1 a.id,a.chdm,isnull(a.bz,'') bz,CONVERT(varchar, a.rq, 102) rq ,b.chmc,
                    b.dw dadw,b.fk dafk,b.kez dakez,a.fs,a.gh,a.zl,
                    a.mlbz,a.tmdw,a.tmlb,a.fk,a.kz,
                    a.ddh,a.sl,b.tp datp,xj.mc  daxjchlbmc,ys.mc ys
                from cl_t_cltmb a 
                inner join CL_T_chdmb b on a.chdm=b.chdm and b.tzid=@zbid               
                left outer join cl_v_chlb as xj on b.xjchlbid=xj.id
                left join YX_T_Shdmb ys on b.ysid=ys.id
                where a.id=@id
                end 
                else begin
                SELECT @id=a.id FROM  cl_t_wltmb a WHERE a.tm=@tmcode or a.chdm=@tmcode
                select top 1 a.id,a.chdm,isnull(a.bz,'') bz,CONVERT(varchar, a.rq, 102) rq ,b.chmc,
                    b.dw dadw,b.fk dafk,b.kez dakez,a.fs,a.gh,a.zl,
                    a.mlbz,a.tmdw,a.tmlb,a.fk,a.kz,
                    a.ddh,a.sl,b.tp datp,xj.mc  daxjchlbmc,ys.mc ys
                from cl_t_wltmb a 
                inner join CL_T_chdmb b on a.chdm=b.chdm and b.tzid=@zbid               
                left outer join cl_v_chlb as xj on b.xjchlbid=xj.id
                left join YX_T_Shdmb ys on b.ysid=ys.id
                where a.id=@id
                end
            ";

            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@zbid", 1));
            paras.Add(new SqlParameter("@tmcode", tmcode));
            DataTable dt = null;

            if (tzid == "11360")
            {
                errInfo = dal.ExecuteQuerySecurity(sql, paras, out dt);
            }
            else
            {
                errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
            }
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    //rtMsg = dt.Rows[0][0].ToString();
                    result.Data = JsonConvert.SerializeObject(dt, new DataTableConverter());
                }
                else
                {
                    result.Errcode = 10001;
                    result.Errmsg = "查询不到该条码信息！";
                }
            }
            else
            {
                result.Errcode = 10000;
                result.Errmsg = "查询条码信息时出错！" + errInfo;
            }
        }

        return JsonConvert.SerializeObject(result); ;
    }

    //条码生成模块的条码生成函数    
    [WebMethod(Description = "条码生成模块的条码生成函数")]
    public string GenerateTM(string SourceID, double sl, string zl, string kh, string bz, string zdr,string fk)
    {
        string rtMsg = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            string str_sql = @"declare @strTm varchar(20),@bz1 varchar(100);
								SELECT TOP 1 @bz1=CASE WHEN SUBSTRING(value,1,1)='扫' AND SUBSTRING(value,LEN(value)-3,4)='条码生成' THEN '' ELSE value END
								FROM dbo.SplitToTable(@bz,',') ORDER BY id DESC 
                                set @strTm=right(CONVERT(varchar(12), getdate(), 112),6) + isnull((select top 1 right(tm,5) as b from cl_t_wltmb where CONVERT(varchar(12),rq,112)=CONVERT(varchar(12),getdate(),112) and left(tm,1) not in ('8','9') order by id desc),'00000'); 
                                insert cl_t_wltmb(tzid,tmlb,chdm,bz,tmdw,rq,sl,tm,syzt,zdr,zl,fs,gh,mlbz,fk,kz,ddh,sxsl) 
                                select 1,tmlb,chdm,'扫'+tm+'条码生成,'+@bz1,tmdw,getdate(),@sl,cast(@strTm as bigint)+1,0,@zdr,@zl,fs,gh,'',@fk,kz,@kh,sxsl from cl_t_wltmb where id=@id;
                                select cast(@strTm as bigint)+1;";//SCOPE_IDENTITY();
            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@sl", sl));
            paras.Add(new SqlParameter("@zdr", zdr));
            paras.Add(new SqlParameter("@zl", zl));
            paras.Add(new SqlParameter("@kh", kh));
            paras.Add(new SqlParameter("@bz", bz));
            paras.Add(new SqlParameter("@fk", fk));
            paras.Add(new SqlParameter("@id", SourceID));
            DataTable dt = null;
            string errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    rtMsg = dt.Rows[0][0].ToString();
                }
                else
                    rtMsg = "Error:生成失败！newid=0";
            }
            else
                rtMsg = "Error:" + errInfo;
        }//end using

        return rtMsg;
    }

    //查询单据是否有未保存的条码   
    [WebMethod(Description = "查询单据是否有未保存的条码2")]
    public string checkDJInfo2String(string  cldjhStr, string tzid)
    {
        // 将JSON字符串转换为DataCldjh对象
        List<DataCldjh> dataCldjhList = JsonConvert.DeserializeObject<List<DataCldjh>>(cldjhStr);
        DataTable datacldjh= new DataTable();
        // 添加列
        datacldjh.Columns.Add("djtm", typeof(int));
        datacldjh.Columns.Add("djly", typeof(string));
        // 添加数据行
        foreach(DataCldjh dr in dataCldjhList)
        {
            //新建行的赋值
            DataRow dtr = datacldjh.NewRow();//创建新行
            dtr["djtm"] = dr.djtm;
            dtr["djly"] = dr.djly;
            datacldjh.Rows.Add(dtr);
        }
        return checkDJInfo(datacldjh, tzid);
    }
    //查询单据是否有未保存的条码   
    [WebMethod(Description = "查询单据是否有未保存的条码")]
    public string checkDJInfo(DataTable datacldjh, string tzid)
    {
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "checkDJInfo-datacldjh.Columns:"+datacldjh.Columns);
        DataRow[] testDr = datacldjh.Select("", "djtm");
        foreach (DataColumn column in datacldjh.Columns)
        {
            nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-datacldjh.Columns_value:"+column.ColumnName);
            nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "setCLInfo-datacldjh."+column.ColumnName+":"+testDr[0][column.ColumnName]);
        }
        string rtMsg = "", errInfo = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            if (datacldjh.Rows.Count > 0)
            {
                string ids = "", ids1 = "", ids3 = "";
                DataRow[] dr = datacldjh.Select("", "djtm");
                List<SqlParameter> para = new List<SqlParameter>();
                DataTable dt = null;
                for (int i = 0; i < dr.Length; i++)
                {
                    if (dr[i]["djly"].ToString() == "S")
                    {
                        //ids += dr[i]["djtm"].ToString().Substring(1, dr[i]["djtm"].ToString().Length - 1) + ",";
                        ids += dr[i]["djtm"].ToString() + ",";
                    }
                    if (dr[i]["djly"].ToString() == "1")
                    {
                        ids1 += dr[i]["djtm"].ToString() + ",";
                    }
                    if (dr[i]["djly"].ToString() == "3")
                    {
                        ids3 += dr[i]["djtm"].ToString() + ",";
                    }
                }

                string str_sql = "";

                if (ids != "")
                {
                    ids = ids.Substring(0, ids.Length - 1);
                    str_sql = @" declare @djids varchar(max); 
                        SELECT @djids= (SELECT SUBSTRING(ids,2,LEN(ids))+',' FROM cl_t_cwdjtmIDgl WHERE id IN (@ids) FOR  XML PATH(''))

                        IF(LEN(@djids)!=0)
                         SELECT  @djids=SUBSTRING(@djids,0,LEN(@djids))
                        ELSE 
                         SELECT @djids=0

                        select a.* into #myzb from cl_v_dddjmx a where a.id IN (SELECT value FROM dbo.SplitToTable(@djids,','))               
                        select distinct a.scddbh,a.chdm,a.sl,a.mxid,a.ckid,xx.id cwid,0 lyscddbh,0 dfckid,a.djlx
                        from #myzb a 
                        inner join cl_v_chdmb c on a.chdm=c.chdm 
                        left join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid  
                        left join cl_t_cwxxb xx on xx.tzid=1 and xx.chdm=a.chdm and xx.gzh=a.scddbh and xx.ckid=a.ckid 
                        left join cl_t_pdakcdj pda on a.mxid=pda.lymxid and a.djlx=pda.lydjlx
                        where c.tzid=@tzid and a.djlx=624 and isnull(pda.id,'')=''
                        order by a.mxid 
                        drop table #myzb";
                    para.Add(new SqlParameter("@ids", ids));
                    para.Add(new SqlParameter("@tzid", tzid));

                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }
                else if (ids1 != "")
                {
                    ids1 = ids1.Substring(0, ids1.Length - 1);
                    str_sql = @" SELECT b.*
                        into #myzb 
                        FROM CL_T_dddjb a 
                        inner JOIN cl_v_dddjmx b ON a.id=b.id
                        where (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max))) IN (@ids1)
                        AND a.djlx=624
                          
                        select distinct a.scddbh,a.chdm,a.sl,a.mxid,a.ckid,xx.id cwid,0 lyscddbh,0 dfckid,a.djlx   
                        from #myzb a 
                        inner join cl_v_chdmb c on a.chdm=c.chdm 
                        left join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid  
                        left join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=a.chdm and xx.gzh=a.scddbh and xx.ckid=a.ckid 
                        LEFT JOIN dbo.cl_T_pdakcdj pda ON a.mxid=pda.lymxid AND a.djlx=pda.lydjlx
                        where c.tzid=1 and a.djlx=624 AND ISNULL(pda.id,'')=''
                        order by a.mxid 
                        drop table #myzb";
                    para.Add(new SqlParameter("@ids1", ids1));
                    para.Add(new SqlParameter("@tzid", tzid));

                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }
                else if (ids3 != "")
                {
                    ids3 = ids3.Substring(0, ids3.Length - 1);
                    str_sql = @" select distinct isnull(a.lyscddbh,0) scddbh,a.chdm,a.sl,a.mxid,isnull(a.dfckid,0) ckid,xx.id cwid,isnull(a.lyscddbh,0) lyscddbh,isnull(a.dfckid,0) dfckid ,a.djlx 
                        from cl_v_kcdjmx a  
                        inner join cl_v_chdmb c on a.chdm=c.chdm and c.tzid=1 
                        inner join cl_t_chdmb cc on c.chdm=cc.chdm 
                        inner join  cl_T_chgzhkcmx ch on a.chdm=ch.chdm AND a.scddbh=ch.gzh AND a.ckid=ch.ckid
                        inner join cl_t_cwxxb xx on xx.tzid=@tzid and xx.chdm=ch.chdm and a.lyscddbh=xx.gzh and xx.ckid=a.dfckid
                        LEFT JOIN dbo.cl_T_pdakcdj pda ON a.mxid=pda.lymxid AND a.djlx=pda.lydjlx 
                        where  a.id in (@ids3) AND a.djlx=561 AND ISNULL(pda.id,'')=''
                        order by a.mxid";
                    para.Add(new SqlParameter("@ids3", ids3));
                    para.Add(new SqlParameter("@tzid", tzid));

                    errInfo = dal.ExecuteQuerySecurity(str_sql, para, out dt);
                }

                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            rtMsg += "\r\n" + dt.Rows[i]["chdm"].ToString() + " , " + dt.Rows[i]["scddbh"].ToString();
                        }
                        dt = null;
                    }
                    else
                    {
                        rtMsg = "over:已全部保存！";
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + str_sql);
                }


            }
        }
        return rtMsg;
    }


    //五里预缩信息查询
    [WebMethod(Description = "五里预缩信息查询")]
    public String getWLCLxx(string tmcode, string tzid)
    {
        string rtMsg = "", errInfo = "";
        //Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {

            dal.ConnectionString = connStr;
            string str_sql = @"
                select chdm,ddh,sl,id,tm from cl_t_cltmb where tm=@tmcode and tzid=@tzid
            ";
            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@tmcode", tmcode));
            paras.Add(new SqlParameter("@tzid", tzid));
            DataTable dt = null;
            errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    //result.Data = JsonConvert.SerializeObject(dt, new DataTableConverter());
                    dt.TableName = "cl_v_dddjmx";
                    rtMsg = SerializeDataTableXml(dt);
                    dt = null;
                }
                else
                {
                    rtMsg = "error:查询无记录！";
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
            }
        }

        return rtMsg;
    }

    //五里预缩保存
    [WebMethod(Description = "五里预缩保存")]
    public String setWLysxx(DataTable tmInfo, string username, string tzid)
    {
        string rtMsg = "", errInfo = "";
        //Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {

            dal.ConnectionString = connStr;
            if (tmInfo != null)
            {
                DataRow[] dr = tmInfo.Select("", "tm");
                DataTable dt = null;
                string errmsg = "";
                StringBuilder sb = new StringBuilder();

                for (int i = 0; i < dr.Length; i++)
                {
                    sb.Append(" select * from cl_t_pdakcdj where tm ='" + dr[i]["tm"] + "' and tzid ='" + tzid + "' and djlx=2471");
                    if (i < dr.Length - 1)
                    {
                        sb.Append(" union all ");
                    }
                }

                errmsg = dal.ExecuteQuery(sb.ToString(), out dt);
                DataRow[] istm = dt.Select("", "tm");
                if (errmsg == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        return "havetm:该条码已经保存过!\r\n条码：" + istm[0]["tm"] + ",\r\n材料：" + istm[0]["chdm"] + ",\r\n订单号：" + istm[0]["gzh"];
                    }
                }
                else
                {
                    rtMsg = "error:" + errmsg;
                    writeLog(rtMsg + "\r\n" + sb);
                    return rtMsg;
                }
                dt = null;

                StringBuilder sql = new StringBuilder();
                for (int i = 0; i < dr.Length; i++)
                {
                    sql.Append(" insert into cl_t_pdakcdj ( djlx, tm, chdm, gzh, sl, lymxid, zdr, zdrq, tzid, bz) values ( 2471, '" + dr[i]["tm"] + "','" + dr[i]["chdm"] + "', '" + dr[i]["ddh"] + "', '" + dr[i]["sl"] + "','" + dr[i]["id"] + "','" + username + "',getdate(),'" + tzid + "','五里PDA预缩')");
                }

                errInfo = dal.ExecuteQuery(sql.ToString(), out dt);
                if (errInfo == "")
                {
                    rtMsg = "保存成功！";
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + sql.ToString());
                }

            }
            else
            {
                rtMsg = "error:数据不存在！";
            }
        }

        return rtMsg;
    }

    //PDA盘点获取条码信息
    [WebMethod(Description = "PDA盘点获取条码信息")]
    public String getChInfo(string tm, string tzid, string ckid, string cw)
    {
        string rtMsg = "", errInfo = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            string str_sql = @"CREATE TABLE #tmp(
	                            [chdm] [varchar](20) NULL,
                                [chmc] [varchar](500) COLLATE Chinese_PRC_CI_AS NULL,
	                            [sl] [float] NULL,
	                            [ckid] [int] NOT NULL,
	                            [cw] [varchar](20) NOT NULL,
	                            [tm] [varchar](30) NOT NULL,
                                [scddbh] [varchar](500) NULL,
                                [zdr] [varchar](20) COLLATE Chinese_PRC_CI_AS NULL
                                )
                                    SELECT * into #info FROM (
                                      select a.chdm,b.chmc,ISNULL(a.sl,0) sl,@ckid ckid,@cw cw,@tm tm,a.ddh scddbh,a.zdr
                                      from cl_t_wltmb a  
                                      inner join cl_t_chdmb b on a.chdm=b.chdm                               
                                      where a.tm=@tm
                                      union                        
                                      SELECT c.chdm,d.chmc,SUM(b.sl) sl ,@ckid ckid,@cw cw,@tm tm,c.ddh scddbh,max(a.slr) zdr
                                      FROM cl_t_wlkb a
                                      LEFT JOIN   cl_t_wlkmxb b on a.id=b.id 
                                      INNER JOIN dbo.cl_t_wltmb c ON b.tm=c.tm
                                      inner join cl_t_chdmb d on c.chdm = d.chdm
                                      WHERE a.wlkbh=@tm 
                                      GROUP BY c.chdm,d.chmc,c.ddh
                                      union
                                      SELECT a.chdm,b.chmc,sum(wl.sl) sl,@ckid ckid,@cw cw,@tm tm,a.scddbh,max(wl.zdr) zdr
                                      FROM dbo.cl_v_dddjmx a
                                      INNER JOIN cl_t_wlzxd wl ON a.id=wl.lyid AND a.mxid = wl.lymxid
                                      inner join cl_t_chdmb b on a.chdm=b.chdm 
                                      where wl.zxxh =@tm
                                      group by a.chdm,b.chmc,a.scddbh
                                    ) a

                                    IF EXISTS (SELECT 1 FROM #info)
                                       insert #tmp
                                       SELECT * FROM #info
                                    else
                                       insert #tmp
                                       SELECT a.chdm,c.chmc,sum(a.sl) sl,@ckid ckid,@cw cw,@tm tm,a.ddh scddbh,max(a.zdr) zdr
                                       FROM dbo.cl_t_wltmb a 
                                       INNER JOIN cl_t_wlsmjlb b ON a.tm=b.sm AND a.zxxh=b.zxxh
                                       inner join cl_t_chdmb c on a.chdm=c.chdm
                                       WHERE b.zxxh=@tm
                                       group by a.chdm,c.chmc,a.ddh

                                      select * from #tmp;
                                      drop table #tmp;";

            List<SqlParameter> paras = new List<SqlParameter>();
            paras.Add(new SqlParameter("@tm", tm));
            paras.Add(new SqlParameter("@tzid", tzid));
            paras.Add(new SqlParameter("@ckid", ckid));
            paras.Add(new SqlParameter("@cw", cw));            
            DataTable dt = null;
            errInfo = dal.ExecuteQuerySecurity(str_sql, paras, out dt);
                writeLog(rtMsg +" tm:"+tm+" tzid:"+tzid+" ckid:"+ckid+" cw:"+cw+ "\r\n" + str_sql.ToString());
                
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    dt.TableName = "pdapd";
                    rtMsg = SerializeDataTableXml(dt);
                    dt = null;
                }
                else
                {
                    rtMsg = "error:查询无记录！";
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
            }

            return rtMsg;
        }
    }

    //保存PDA盘信息
    [WebMethod(Description = "保存PDA盘信息")]
    public String setPDAPD(string pdxx, string cname, string tzid, string ckid, string cwtm)
    {
        string rtMsg = "", errInfo = "";
        string sql = @"";
        string str_sql = @"";
        DataTable dt = null;
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            List<PDAPD> pdInfo = XmlDeSerialize<List<PDAPD>>(pdxx);

            for (int i = 0; i < pdInfo.Count; i++)
            {
                str_sql += @" select tm,chdm from cl_t_pdakcdj where djlx=2465 and tm='" + pdInfo[i].tm + "' and chdm='" + pdInfo[i].chdm + "' and gzh='" + pdInfo[i].scddbh + "' and DATEDIFF(day,zdrq,GETDATE())=0 and tzid=" + tzid;
                if (i < pdInfo.Count - 1)
                {
                    str_sql += @" union all ";
                }
            }
            errInfo = dal.ExecuteQuery(str_sql, out dt);
            DataRow[] htm = dt.Select("", "");
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    return "havetm:该条码已经保存过!\r\n条码：" + htm[0]["tm"] + ",\r\n材料：" + htm[0]["chdm"];
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
                writeLog(rtMsg + "\r\n" + sql);
                return rtMsg;
            }

            sql += @"declare @id int; declare @zbid int; 
                    select @id=a.id from cl_T_pdakcdjzb a where a.djlx=2465 and a.shbs=0 and a.tzid=@tzid and ckid=@ckid and cw=@cwtm;
                    if @id is null 
                     begin                         
                        insert into cl_T_pdakcdjzb (rq, zdr, zdrq, pdrq, shbs, shr, shrq, qrbs, qrr, qrrq, qpbs, ckid, cw, djlx, tzid) values 
                        (getdate(),@cname,getdate(),getdate(),0,'','',0,'','',1,@ckid,@cwtm,2465,@tzid) 
                        SET @id=SCOPE_IDENTITY() 
                    end ";

            foreach (PDAPD pd in pdInfo)
            {
                sql += @" insert into cl_T_pdakcdj (djlx,tm,chdm,sl,cw,zdr,zdrq,ckid,tzid,bz,gzh,zbid) values (2465,'{0}','{1}','{2}','{3}',@cname,getdate(),'{4}',@tzid,'{6}','{5}',@id)";
                sql = string.Format(sql, pd.tm, pd.chdm, pd.sl, pd.cw, pd.ckid, pd.scddbh, pd.zdr);
            }

            dt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            para.Add(new SqlParameter("@cname", cname));
            para.Add(new SqlParameter("@ckid", ckid));
            para.Add(new SqlParameter("@cwtm", cwtm));
            errInfo = dal.ExecuteQuerySecurity(sql, para, out dt);
            if (errInfo == "")
            {
                rtMsg = "保存成功";
            }
            else
            {
                rtMsg = "error:" + errInfo;
                writeLog(rtMsg + "\r\n" + sql.ToString());
            }
            return rtMsg;
        }
    }

    //盘点仓库
    [WebMethod(Description = "盘点仓库")]
    public string getPDCKXX(string tzid)
    {
        string rtMsg = "", errInfo = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                string str_sql = @" select cast(id as varchar) as dm,mc,dm as xh from cl_V_ckdmb  where tzid={0} and ty=0 and id in (2171,10664) order by id desc ";
                DataTable dt2 = null;
                str_sql = string.Format(str_sql, tzid);
                errInfo = dal.ExecuteQuery(str_sql, out dt2);
                if (errInfo == "")
                {
                    if (dt2.Rows.Count > 0)
                    {
                        dt2.TableName = "cl_V_ckdmb";
                        rtMsg = SerializeDataTableXml(dt2);
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                }
            }
        }

        return rtMsg;
    }

    //PDA接收数据检查
    [WebMethod(Description = "PDA接收")]
    public string PDAjsCheck(string tm, string tzid, string djlx, string tlfl)
    {
        string rtMsg = "", msg = "", errInfo = "", sql = " ";
        string tmid;
        string str_sql = "";
        if (tzid == "")
            rtMsg = "error:tzid参数为空！";
        else
        {
            using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
            {
                dal.ConnectionString = connStr;
                if (djlx == "624")
                {
                    if (tm.Substring(0, 1) == "S")
                    {
                        tmid = tm.Substring(1, tm.Length - 1);
                        sql += " DECLARE @ids VARCHAR(max) ";
                        sql += " select @ids = SUBSTRING(a.ids,2,LEN(a.ids)) from cl_t_cwdjtmIDgl a where id='" + tmid + "' and djlx=624 ";
                        sql += " SELECT items id INTO #ids FROM dbo.f_Split(@ids,',') ";
                        sql += " IF EXISTS (SELECT 1 FROM dbo.CL_v_dddjmx a INNER JOIN #ids b ON a.id=b.id WHERE a.djlx=624 AND a.djlb=6678 and a.zfkhid <>11360) ";
                        sql += " SELECT TOP 1 kh.khmc FROM CL_v_dddjmx a INNER JOIN #ids b ON a.id=b.id AND a.djlx=624 INNER JOIN yx_t_khb kh ON a.zfkhid=kh.khid ";
                        sql += " else  ";
                        sql += " select 1 from cl_t_cwdjtmIDgl where id='" + tmid + "' and djlx=624 ";
                        sql += " drop table #ids ";
                    }
                    else
                    {
                        sql += " if exists (SELECT 1 FROM cl_v_dddjmx a where (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))='" + tm + "' AND a.djlx=624 and a.djlb=6678 and a.zfkhid<>11360) ";
                        sql += " SELECT TOP 1 kh.khmc FROM cl_v_dddjmx a INNER JOIN yx_t_khb kh ON a.zfkhid=kh.khid where (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))='" + tm + "' AND a.djlx=624 ";
                        sql += " else ";
                        sql += " SELECT 1 FROM CL_T_dddjb a inner JOIN cl_v_dddjmx b ON a.id=b.id where (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))='" + tm + "' AND a.djlx=624";
                    }
                }
                else if (djlx == "605")
                {
                    if (tm.Substring(0, 1) == "F")
                    {
                        tmid = tm.Substring(1, tm.Length - 1);
                        sql += " DECLARE @ids VARCHAR(max) ";
                        sql += " select @ids = SUBSTRING(a.ids,2,LEN(a.ids)) from cl_t_cwdjtmIDgl a where id='" + tmid + "' and djlx=605 ";
                        sql += " SELECT items id INTO #ids FROM dbo.f_Split(@ids,',') ";
                        sql += " if exists ( select 1 from cl_t_dddjb a inner join #ids b on a.id=b.id inner join cl_t_dddjb c on a.lydjlx=c.djlx and a.lydjid=c.id and c.djlx=624 and c.djlb=6678 where a.djlx=605 ) ";
                        sql += " select TOP 1 kh.khmc from cl_t_dddjb a inner join #ids b on a.id=b.id INNER JOIN dbo.yx_t_khb kh ON a.khid=kh.khid WHERE a.djlx=605 ";
                        sql += " else ";
                        sql += " select 1 from cl_t_cwdjtmIDgl where id='" + tmid + "' and djlx=605 ";
                        sql += " drop table #ids ";
                    }
                    else
                    {
                        sql += " if exists (select 1 from cl_v_dddjmx a inner join cl_v_dddjmx b on a.mxid=b.lymxid and b.djlx=2250 ";
                        sql += "     inner join cl_t_dddjb c on a.lydjid=c.id and a.lydjlx=c.djlx and c.djlx=624 and c.djlb=6678 where a.djlx=605 and b.id='" + tm + "') ";
                        sql += " select top 1 kh.khmc from cl_v_dddjmx a inner join cl_v_dddjmx b on a.mxid=b.lymxid and b.djlx=2250 INNER JOIN yx_t_khb kh ON a.khid=kh.khid where a.djlx=605 and b.id='" + tm + "' ";
                        sql += " else ";
                        sql += " select 1 from cl_v_dddjmx a inner join cl_v_dddjmx b on a.mxid=b.lymxid and b.djlx=2250 where a.djlx=605 and b.id='" + tm + "'";
                    }
                }

                DataTable dt = null;
                errInfo = dal.ExecuteQuery(sql, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count <= 0)
                    {
                        rtMsg = "error: 无效条码";
                        return rtMsg;
                    } else if (dt.Rows[0][0].ToString() != "1"){
                        rtMsg = "error: "+dt.Rows[0][0].ToString()+" 直发单，无效条码";
                        return rtMsg;
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    return rtMsg;
                }

                str_sql += " select tm,zdr,zdrq from cl_T_pdajs where tzid=" + tzid + " and djlx=" + djlx + " and istl=" + tlfl + " and tm='" + tm + "' ";
                dt = null;
                errInfo = dal.ExecuteQuery(str_sql, out dt);
                if (errInfo == "")
                {
                    if (dt.Rows.Count > 0)
                    {
                        rtMsg = "error: 该条码已接收！\r\n条码：" + dt.Rows[0][0] + "\r\n制单人：" + dt.Rows[0][1] + "\r\n制单日期：" + dt.Rows[0][2];
                        return rtMsg;
                    }
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    return rtMsg;
                }
            }
        }
        return msg;
    }

    //PDA接收保存
    [WebMethod(Description = "保存PDA接单信息")]
    public String setPDAJS(string pdxx, string cname, string tzid, string djlx, string xzry, string fcfa, string tlfl)
    {
        string rtMsg = "", errInfo = "";
        string sql = "";
        string str_sql = @"";
        DataTable dt = null;
        nrWebClass.clsLoger.WriteLog("0", "", "WSZXDDataPull.asmx", "pdxx:"+pdxx+",canme:"+cname+",tzid:"+tzid+",djlx:"+djlx+",xzry:"+xzry+",fcfa:"+fcfa+",tlfl:"+tlfl);
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            List<PDAJS> pdInfo = XmlDeSerialize<List<PDAJS>>(pdxx);

            for (int i = 0; i < pdInfo.Count; i++)
            {
                str_sql += @" select tm,zdr,zdrq from cl_T_pdajs where djlx=" + djlx + " and tm='" + pdInfo[i].tm + "' and tzid=" + tzid + " and istl=" + tlfl;
                if (i < pdInfo.Count - 1)
                {
                    str_sql += @" union all ";
                }
            }
            errInfo = dal.ExecuteQuery(str_sql, out dt);
            DataRow[] htm = dt.Select("", "");
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    return "havetm:该条码已经保存过!\r\n条码：" + htm[0]["tm"] + ",\r\n制单人：" + htm[0]["zdr"] + ",\r\n日期：" + htm[0]["zdrq"];
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
                writeLog(rtMsg + "\r\n" + sql);
                return rtMsg;
            }

            sql += " declare @ids varchar(max); declare @fcbl decimal(9,3); select distinct items ryid into #xzry from dbo.f_Split('" + xzry + "',','); ";
            sql += " select @fcbl= case @fcfa when 1 then CAST(ROUND(1.0/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) ELSE CAST(ROUND(0.9/(SELECT COUNT(1)+1 FROM #xzry),3,1) AS DECIMAL(9,3)) END; ";

            foreach (PDAJS pd in pdInfo)
            {
                string uuid = System.Guid.NewGuid().ToString("N");
                if (djlx == "624")
                {
                    sql += " if SUBSTRING('{0}',1,1)='S' ";
                    sql += " SELECT @ids=SUBSTRING(ids,2,LEN(ids)) FROM cl_t_cwdjtmIDgl WHERE id=SUBSTRING('{0}',2,LEN('{0}')) else ";
                    sql += " SELECT @ids=id FROM dbo.CL_T_dddjb WHERE djlx=624 and (CONVERT(varchar(6),zdrq,112)+CAST(djh AS VARCHAR(6)))='{0}' ";
                }
                else if (djlx == "605")
                {
                    sql += " if SUBSTRING('{0}',1,1)='F' ";
                    sql += " SELECT @ids=SUBSTRING(ids,2,LEN(ids)) FROM cl_t_cwdjtmIDgl WHERE id=SUBSTRING('{0}',2,LEN('{0}')) else select @ids='{0}' ";
                }

                sql += " insert into cl_T_pdajs (uuid, djlx, tm, zdr, zdrq, istl, tzid, fcfa ) values ('" + uuid + "', @djlx, '{0}', @cname, getdate(), '" + tlfl + "', @tzid, @fcfa); ";
                sql += " insert into cl_t_pdajsmx (uuid, mxid ) select '" + uuid + "', id from cl_t_dddjb where id in ( SELECT * FROM dbo.f_Split(@ids,',') ) ";
                sql += " insert into cl_T_pdaxzry ( uuid, ryid, fcbl ) select '" + uuid + "' ,ryid,@fcbl from #xzry ";

                sql = string.Format(sql, pd.tm);
            }

            dt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            para.Add(new SqlParameter("@tzid", tzid));
            para.Add(new SqlParameter("@cname", cname));
            para.Add(new SqlParameter("@djlx", djlx));
            para.Add(new SqlParameter("@xzry", xzry));
            para.Add(new SqlParameter("@fcfa", fcfa));

            errInfo = dal.ExecuteQuerySecurity(sql, para, out dt);
            if (errInfo == "")
            {
                rtMsg = "保存成功";
            }
            else
            {
                rtMsg = "error:" + errInfo;
                writeLog(rtMsg + "\r\n" + sql.ToString());
            }
            return rtMsg;
        }
    }

    //PDA抽检
    [WebMethod(Description = "PDA抽检单据信息")]
    public String getPDACJ(string djtm, string tzid, string djlx)
    {
        string rtMsg = "", errInfo = "";
        string sql = "";
        string tmid = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            if (djlx == "624")
            {
                if (djtm.Substring(0, 1).ToUpper() == "S")
                {
                    tmid = djtm.Substring(1, djtm.Length - 1);
                    sql += " DECLARE @ids VARCHAR(max); select @ids=SUBSTRING(ids,2,LEN(ids)) from cl_t_cwdjtmIDgl where id='" + tmid + "' and djlx=624 ";
                    sql += " select a.chdm,SUM(a.sl) sl from cl_v_dddjmx a where a.id in (SELECT value FROM dbo.SplitToTable(@ids,','))  GROUP BY a.chdm ";
                }
                else
                {
                    sql += " select a.chdm, sum(sl) sl from cl_v_dddjmx a where a.djlx=624 and a.tzid=1 and (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(10)))='" + djtm + "' group by a.chdm ";
                }
            }
            else if (djlx == "605")
            {
                if (djtm.Substring(0, 1).ToUpper() == "F")
                {
                    tmid = djtm.Substring(1, djtm.Length - 1);
                    sql += " DECLARE @ids VARCHAR(max); select @ids=SUBSTRING(ids,2,LEN(ids)) from cl_t_cwdjtmIDgl where id='" + tmid + "' and djlx=605 ";
                    sql += " select a.chdm,SUM(a.sl) sl from cl_v_dddjmx a where a.id in (SELECT value FROM dbo.SplitToTable(@ids,','))  GROUP BY a.chdm ";
                }
                else
                {
                    sql += " SELECT b.chdm,SUM(b.sl) sl FROM CL_v_dddjmx a INNER JOIN CL_v_dddjmx b ON a.lymxid=b.mxid AND b.djlx=605 WHERE a.id='" + djtm + "' GROUP BY b.chdm ";
                }

            }
            DataTable dt = null;
            errInfo = dal.ExecuteQuery(sql, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    dt.TableName = "pdacj";
                    rtMsg = SerializeDataTableXml(dt);
                    dt = null;
                }
                else
                {
                    rtMsg = "error: 查无数据";
                    return rtMsg;
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
                return rtMsg;
            }
            return rtMsg;
        }
    }

    //PDA抽检条码信息
    [WebMethod(Description = "PDA抽检条码信息")]
    public String getBM(string tm, string tzid)
    {
        string rtMsg = "", errInfo = "";
        string sql = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            sql += " SELECT chdm,sl FROM dbo.cl_t_wltmb WHERE tm='" + tm + "'";

            DataTable dt = null;
            errInfo = dal.ExecuteQuery(sql, out dt);
            if (errInfo == "")
            {
                if (dt.Rows.Count > 0)
                {
                    dt.TableName = "pdacjtm";
                    rtMsg = SerializeDataTableXml(dt);
                    dt = null;
                }
                else
                {
                    rtMsg = "error: 查无数据";
                    return rtMsg;
                }
            }
            else
            {
                rtMsg = "error:" + errInfo;
                return rtMsg;
            }
            return rtMsg;
        }
    }

    //PDA抽检保存
    [WebMethod(Description = "PDA抽检条码信息")]
    public String setPDACJ(string djlx, string djtm, string bm, string cysl, string cjjl, string zdr, string tzid)
    {
        string rtMsg = "", errInfo = "";
        string sql = "";
        int ishz = 0;
        DataTable dt = null;
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            if (djtm.Substring(0, 1).ToString() == "S" || djtm.Substring(0, 1).ToString() == "F")
            {
                ishz = 1;
                djtm = djtm.Substring(1, djtm.Length - 1);
            }

            if (djlx == "624" && ishz == 0)
            {
                string str_sql = " select a.id from cl_t_dddjb a where a.djlx=624 and a.tzid='" + tzid + "' and (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(10)))='" + djtm + "' ";
                dt = null;
                errInfo = dal.ExecuteQuery(str_sql, out dt);
                if (errInfo == "")
                {
                    djtm = dt.Rows[0][0].ToString();
                }
                else
                {
                    rtMsg = "error:" + errInfo;
                    writeLog(rtMsg + "\r\n" + sql.ToString());
                }

            }

            sql += " INSERT INTO dbo.cl_T_pdacj ( tzid, djlx, tm, lydjid, lydjlx, cysl, cjjl, ishz, zdr, zdrq ) values ";
            sql += " ('" + tzid + "',2468,'" + bm + "','" + djtm + "','" + djlx + "','" + cysl + "','" + cjjl + "','" + ishz + "','" + zdr + "',getdate()) ";

            dt = null;
            errInfo = dal.ExecuteQuery(sql, out dt);
            if (errInfo == "")
            {
                rtMsg = "保存成功";
            }
            else
            {
                rtMsg = "error:" + errInfo;
                writeLog(rtMsg + "\r\n" + sql.ToString());
            }
            return rtMsg;
        }
    }

    //条码生成一键打印
    [WebMethod(Description = "条码生成一键打印")]
    public String getDJInfoPrt(string tzid,string cname,string djid,string djlx,string chdm)
    {
        string rtMsg = "", errInfo = "";
        string sql = "";
        string id = "";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            if (djlx == "tzd")
            {
                if (djid.Substring(0, 1) == "S")
                {
                    id = djid.Substring(1, djid.Length-1).ToString();
                    sql += " select b.mxid INTO #temp from cl_t_cwdjtmIDgl a INNER JOIN dbo.cl_t_cwdjtmIDglmx b ON a.id=b.id WHERE a.djlx=624 and a.id=" + id + "; ";
                    sql += " SELECT a.id,a.djh,a.chdm,ch.chmc,sp.spkh SKU,SUM(a.sl) sl,MAX(ch.dw) dadw,'' tm,'' dw,'' mlbz,ch.fk,max(ys.mc) ys FROM cl_v_dddjmx a ";
                    sql += " INNER JOIN #temp b ON a.id=b.mxid ";
                    sql += " INNER JOIN dbo.YX_T_Spcgjhb c ON a.scddbh=c.cggzh ";
                    sql += " INNER JOIN dbo.YX_T_Spdmb sp ON c.sphh=sp.sphh ";
                    sql += " INNER JOIN dbo.CL_T_chdmb ch ON a.chdm=ch.chdm ";
                    sql += " left join YX_T_Shdmb ys on ch.ysid=ys.id ";
                    sql += " WHERE a.djlx=624 and a.chdm like '"+chdm+"%' ";
                    sql += " GROUP BY a.id,a.djh,a.chdm,sp.spkh,ch.chmc,ch.fk ";
                    sql += " ORDER BY a.id,a.chdm ";

                }
                else {
                    sql += " SELECT a.id,a.djh,a.chdm,ch.chmc,sp.spkh SKU,SUM(a.sl) sl,MAX(ch.dw) dadw,'' tm,'' dw,'' mlbz,max(ch.fk) fk,max(ys.mc) ys ";
                    sql += " FROM cl_v_dddjmx a ";
                    sql += " INNER JOIN dbo.YX_T_Spcgjhb c ON a.scddbh=c.cggzh ";
                    sql += " INNER JOIN dbo.YX_T_Spdmb sp ON c.sphh=sp.sphh ";
                    sql += " INNER JOIN dbo.CL_T_chdmb ch ON a.chdm=ch.chdm ";
                    sql += " left join YX_T_Shdmb ys on ch.ysid=ys.id ";
                    sql += " WHERE a.tzid=1 AND (CONVERT(varchar(6),a.zdrq,112)+CAST(a.djh AS VARCHAR(max)))='" + djid + "' AND a.djlx=624 and a.chdm like '"+chdm+"%'  ";
                    sql += " GROUP BY a.id,a.djh,a.chdm,sp.spkh,ch.chmc ";
                    sql += " ORDER BY a.id,a.chdm ";
                }
            }
            else if (djlx == "lyd") {
                if (djid.Substring(0, 1) == "F")
                {
                    id = djid.Substring(1, djid.Length-1).ToString();
                    sql += " select b.mxid INTO #temp from cl_t_cwdjtmIDgl a INNER JOIN dbo.cl_t_cwdjtmIDglmx b ON a.id=b.id WHERE a.djlx=605 and a.id=" + id + "; ";
                    sql += " SELECT a.id,a.djh,a.chdm,ch.chmc,sp.spkh SKU,SUM(a.sl) sl,MAX(ch.dw) dadw,'' tm,'' dw,'' mlbz,max(ch.fk) fk,max(ys.mc) ys FROM cl_v_dddjmx a ";
                    sql += " INNER JOIN #temp b ON a.id=b.mxid ";
                    sql += " INNER JOIN dbo.YX_T_Spcgjhb c ON a.scddbh=c.cggzh ";
                    sql += " INNER JOIN dbo.YX_T_Spdmb sp ON c.sphh=sp.sphh ";
                    sql += " INNER JOIN dbo.CL_T_chdmb ch ON a.chdm=ch.chdm ";
                    sql += " left join YX_T_Shdmb ys on ch.ysid=ys.id ";
                    sql += " WHERE a.djlx=605 and a.chdm like '"+chdm+"%'  ";
                    sql += " GROUP BY a.id,a.djh,a.chdm,sp.spkh,ch.chmc ";
                    sql += " ORDER BY a.id,a.chdm ";
                }
                else {
                    sql += " SELECT a.id,a.djh,a.chdm,ch.chmc,sp.spkh SKU,SUM(a.sl) sl,MAX(ch.dw) dadw,'' tm,'' dw,'' mlbz,max(ch.fk) fk,max(ys.mc) ys ";
                    sql += " FROM dbo.cl_v_dddjmx a ";
                    sql += " INNER JOIN dbo.cl_v_dddjmx b ON b.tzid=1 AND b.djlx=2250 AND a.mxid=b.lymxid ";
                    sql += " INNER JOIN dbo.YX_T_Spcgjhb c ON a.scddbh=c.cggzh ";
                    sql += " INNER JOIN dbo.YX_T_Spdmb sp ON c.sphh=sp.sphh ";
                    sql += " INNER JOIN dbo.CL_T_chdmb ch ON a.chdm=ch.chdm ";
                    sql += " left join YX_T_Shdmb ys on ch.ysid=ys.id ";
                    sql += " WHERE a.tzid=1 AND a.djlx=605 AND b.id=" + djid + " and a.chdm like '"+chdm+"%'  ";
                    sql += " GROUP BY a.id,a.djh,a.chdm,sp.spkh,ch.chmc ";
                    sql += " ORDER BY a.id,a.chdm ";
                }
            }
            //writeLog(rtMsg + "\r\n" + sql.ToString());
            List<SqlParameter> paras = new List<SqlParameter>();
            DataTable dt = null;
            errInfo = dal.ExecuteQuerySecurity(sql, paras, out dt);
            if (errInfo != "")
            {
                rtMsg = "error:查询单据信息出错！" + errInfo;
                return rtMsg;
            }
            if (dt.Rows.Count == 0)
            {
                rtMsg = "error:查询不到该单据信息！";
                return rtMsg;
            }
            rtMsg = SetPLTMsc(dt, cname);
            return rtMsg;
        }
    }

    //条码批量生成
    public string SetPLTMsc(DataTable dt,string cname) {
        string rtMsg = "",errInfo="";
        string sql = " declare @strTm varchar(20);declare @date datetime;set @date =getdate(); ";
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            List<string> sb = new List<string>();
            for (int i = 0; i < dt.Rows.Count; i++) {
                sb.Add(" select "+i+" xh,'" + dt.Rows[i]["id"].ToString() + "' id,'" + dt.Rows[i]["djh"].ToString() + "' djh,'" + dt.Rows[i]["chdm"].ToString() + "' chdm,'" + dt.Rows[i]["chmc"].ToString() + "' chmc,'" + dt.Rows[i]["sku"].ToString() + "' sku,'" + dt.Rows[i]["sl"].ToString() + "' sl,'" + dt.Rows[i]["dadw"].ToString() + "' dadw,'" + dt.Rows[i]["mlbz"].ToString() + "' mlbz,'" + dt.Rows[i]["fk"].ToString() + "' fk ");
            }
            sql += " SELECT a.* INTO #temp FROM  (" + string.Join(" union ", sb.ToArray()) + ") a  ";
            sql += " SET @strTm=right(CONVERT(varchar(12), getdate(), 112),6) + isnull((select top 1 right(tm,5) as b from cl_t_wltmb where CONVERT(varchar(12),rq,112)=CONVERT(varchar(12),getdate(),112)  and left(tm,1) not in ('8','9') order by right(tm,5) desc),'00000');";
            sql += " INSERT into cl_t_wltmb(tzid,tmlb,chdm,bz,tmdw,rq,sl,tm,syzt,zdr,ddh,fk)  ";
            sql += " SELECT 1,MAX(a.tmlb),a.chdm,b.djh,b.dadw,@date,b.sl,cast(@strTm as bigint)+1+b.xh,0,'" + cname + "',b.SKU,b.fk ";
            sql += " FROM dbo.cl_t_wltmb a ";
            sql += " INNER JOIN #temp b on a.chdm=b.chdm ";
            sql += " GROUP BY a.chdm,b.djh,b.chdm,b.SKU,b.xh,b.dadw,b.sl,b.fk ";
            sql += " select b.id,b.djh,b.chdm,b.chmc,b.sku,b.sl,b.dadw,a.tm,a.tmdw dw,b.mlbz,b.fk,ys.mc ys ";
            sql += " from cl_t_wltmb a ";
            sql += " inner join cl_t_chdmb ch on a.chdm=ch.chdm ";
            sql += " left join YX_T_Shdmb ys on ch.ysid=ys.id ";
            sql += " inner join #temp b on a.chdm=b.chdm and a.ddh=b.sku ";
            sql += " where a.rq=@date and a.zdr='" + cname + "'";
            sql += " order by b.id,a.chdm ";

            DataTable dta = null;
            List<SqlParameter> paras = new List<SqlParameter>();
            errInfo = dal.ExecuteQuerySecurity(sql,paras, out dta);
            if (errInfo != "")
            {
                rtMsg = "error:条码生成失败！"+errInfo+": "+sql;
                return rtMsg;
            }
            else {
                if (dta.Rows.Count == 0)
                {
                    rtMsg = "error:无条码生成！";
                    return rtMsg;
                }
                else {
                    dta.TableName = "tmplsc";
                    rtMsg = SerializeDataTableXml(dta);
                }
            }
        }
        return rtMsg;
    }

    /// <summary>
    /// PDA版本号
    /// </summary>
    /// <returns></returns>
    [WebMethod(Description = "PDA版本号")]
    public Result getPDAVer(string s)
    {
        string errInfo = "";
        Result result = new Result();
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;
            //一定要去重
            string str_sql = @"
                select top 1 ver,url from cl_V_pda_ver a where type='{0}' order by ver desc;
";

            DataSet ds = null;
            List<SqlParameter> para = new List<SqlParameter>();
            errInfo = dal.ExecuteQuerySecurity(string.Format(str_sql, s), para, out ds);
            if (errInfo == "")
            {
                if (ds.Tables[0].Rows.Count > 0)
                {
                    ds.Tables[0].TableName = "cl_V_pda_ver";
                    result.Data = SerializeDataTableXml(ds.Tables[0]);
                }
                else
                {
                    //判断是否是直发条码
                    result.Data = "查询无记录！";
                    result.Errcode = 102;
                }
            }
            else
            {
                result.Data = errInfo;
                result.Errcode = 201;
            }
        }
        return result;
    }


    /// <summary>
    /// 库存出库对照
    /// </summary>
    /// <returns></returns>
    [WebMethod(Description = "库存出库对照")]
    public Result getPDAkc(DataTable dt)
    {
        string errInfo = "";
        Result result = new Result();
        result.Errcode = 0;
        DataTable resdt = new DataTable();
        DataColumn dc = null;
        dc = resdt.Columns.Add("gzh", Type.GetType("System.String"));
        dc = resdt.Columns.Add("chdm", Type.GetType("System.String"));
        dc = resdt.Columns.Add("ckid", Type.GetType("System.Int32"));
        dc = resdt.Columns.Add("tzid", Type.GetType("System.Int32"));
        dc = resdt.Columns.Add("tm", Type.GetType("System.String"));
        dc = resdt.Columns.Add("sl", Type.GetType("System.Single"));
        dc = resdt.Columns.Add("cw", Type.GetType("System.String"));
        using (LiLanzDALForXLM dal = new LiLanzDALForXLM())
        {
            dal.ConnectionString = connStr;

            List<string> sb = new List<string>();
            string str_sql = "";
            string[] fieldNames = { "gzh", "chdm", "ckid", "tzid" };
            DataTable istinctDt = Distinct(dt, fieldNames);
            for (int i = 0; i < istinctDt.Rows.Count; i++)
                sb.Add(" select  gzh = '" + istinctDt.Rows[i]["gzh"] + "' , chdm = '" + istinctDt.Rows[i]["chdm"] + "' , ckid = " + istinctDt.Rows[i]["ckid"] + " , tzid = " + istinctDt.Rows[i]["tzid"]);

            str_sql += " SELECT A.* INTO #TMP FROM  (" + string.Join(" union ", sb.ToArray()) + ") A  ";
            str_sql += " select a.* from cl_T_pdakc a inner join  #TMP b on a.gzh=b.gzh and a.chdm=b.chdm and a.ckid=b.ckid and a.tzid=b.tzid  ORDER BY a.gzh,a.chdm,a.ckid,a.tzid,a.sl";
            //writeLog("\r\nSQL:" + str_sql);
            DataTable sldt = null;
            List<SqlParameter> para = new List<SqlParameter>();
            string aa = DateTime.Now.ToString();
            errInfo = dal.ExecuteQuerySecurity(str_sql, para, out sldt);
            string bb = DateTime.Now.ToString();
            //writeLog("\r\ntime:" + aa + "\r\ntime:" + bb);

            if (errInfo == "")
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    double sl = double.Parse(dt.Rows[i]["sl"].ToString());
                    DataRow[] drs = sldt.Select("gzh = '" + dt.Rows[i]["gzh"] + "' and chdm = '" + dt.Rows[i]["chdm"] + "' and ckid = " + dt.Rows[i]["ckid"] + " and tzid = " + dt.Rows[i]["tzid"]);
                    if (drs.Length > 0)
                    {
                        for (int j = 0; j < drs.Length; j++)
                        {
                            DataRow newRow = resdt.NewRow();
                            newRow["tm"] = dt.Rows[i]["tm"];
                            newRow["tzid"] = dt.Rows[i]["tzid"];
                            newRow["ckid"] = dt.Rows[i]["ckid"];
                            newRow["gzh"] = dt.Rows[i]["gzh"];
                            newRow["chdm"] = dt.Rows[i]["chdm"];
                            newRow["cw"] = drs[j]["cw"];
                            if (sl >= double.Parse(drs[j]["sl"].ToString()))
                            {
                                newRow["sl"] = double.Parse(drs[j]["sl"].ToString());
                                resdt.Rows.Add(newRow);
                                sl -= double.Parse(drs[j]["sl"].ToString());
                                //writeLog("\r\nsl:" + sl  );
                                drs[j]["sl"] = 0;
                                if (sl == 0) break;
                            }
                            else if (sl < double.Parse(drs[j]["sl"].ToString()))
                            {
                                newRow["sl"] = sl;
                                resdt.Rows.Add(newRow);
                                drs[j]["sl"] = double.Parse(drs[j]["sl"].ToString()) - sl;
                                sl = 0;
                                break;
                            }
                        }
                        if (sl > 0)
                        {
                            //仓位库存数量不足
                            DataRow newRow = resdt.NewRow();
                            newRow["tm"] = dt.Rows[i]["tm"];
                            newRow["tzid"] = dt.Rows[i]["tzid"];
                            newRow["ckid"] = dt.Rows[i]["ckid"];
                            newRow["gzh"] = dt.Rows[i]["gzh"];
                            newRow["chdm"] = dt.Rows[i]["chdm"];
                            newRow["cw"] = "仓位不足";
                            newRow["sl"] = sl;
                            resdt.Rows.Add(newRow);
                        }

                    }
                    else
                    {
                        //库存无数据
                        DataRow newRow = resdt.NewRow();
                        newRow["tm"] = dt.Rows[i]["tm"];
                        newRow["tzid"] = dt.Rows[i]["tzid"];
                        newRow["ckid"] = dt.Rows[i]["ckid"];
                        newRow["gzh"] = dt.Rows[i]["gzh"];
                        newRow["chdm"] = dt.Rows[i]["chdm"];
                        newRow["cw"] = "仓位不足";
                        newRow["sl"] = sl;
                        resdt.Rows.Add(newRow);
                    }
                }
                resdt.TableName = "zxckcw";
                //result.Data = SerializeDataTableXml(resdt);
                result.Data = resdt;

            }
            else
            {
                result.Errmsg = "error:" + errInfo;
                result.Errcode = 201;
            }
        }
        return result;
    }

    public static DataTable Distinct(DataTable dt, string[] filedNames)
    {
        DataView dv = dt.DefaultView;
        DataTable DistTable = dv.ToTable("Dist", true, filedNames);
        return DistTable;
    }
    /// <summary>
    /// 序列化DataTable
    /// </summary>
    private string SerializeDataTableXml(DataTable pDt)
    {
        //序列化DataTable
        StringBuilder sb = new StringBuilder();
        XmlWriter writer = XmlWriter.Create(sb);
        XmlSerializer serializer = new XmlSerializer(typeof(DataTable));
        serializer.Serialize(writer, pDt);
        writer.Close();
        return sb.ToString();
    }

    /// <summary>
    /// 反序列化DataTable
    /// </summary>
    public static DataTable DeserializeDataTable(string pXml)
    {
        StringReader strReader = new StringReader(pXml);
        XmlReader xmlReader = XmlReader.Create(strReader);
        XmlSerializer serializer = new XmlSerializer(typeof(DataTable));
        DataTable dt = serializer.Deserialize(xmlReader) as DataTable;
        return dt;
    }
    //反序列化函数
    public static T XmlDeSerialize<T>(string objString)
    {
        XmlSerializer serializer = new XmlSerializer(typeof(T));
        MemoryStream ms = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(objString));
        ms.Position = 0;
        T _obj = (T)serializer.Deserialize(ms);
        ms.Close();
        return _obj;
    }




    //写日志文件方法
    public static void writeLog(string info)
    {
        try
        {
            clsLocalLoger.logDirectory = HttpContext.Current.Server.MapPath("logs/");
            if (System.IO.Directory.Exists(clsLocalLoger.logDirectory) == false)
            {
                System.IO.Directory.CreateDirectory(clsLocalLoger.logDirectory);
            }
            clsLocalLoger.WriteInfo(info);
        }
        catch (Exception ex)
        {

        }
    }
}

/// <summary>
/// 数据实体类
/// </summary>
public class ZXDData : IDisposable
{
    //主表信息
    private string _pc;
    private string _bz;
    private string _xzl;
    private string _khdm;

    //明细数据
    private DataTable _mxTable;

    public string pc
    {
        get { return this._pc; }
        set { this._pc = value; }
    }

    public string bz
    {
        get { return this._bz; }
        set { this._bz = value; }
    }

    public string xzl
    {
        get { return this._xzl; }
        set { this._xzl = value; }
    }

    public string khdm
    {
        get { return this._khdm; }
        set { this._khdm = value; }
    }

    public DataTable mxTable
    {
        get { return this._mxTable; }
        set { this._mxTable = value; }
    }


    #region IDisposable 成员
    public void Dispose()
    {
        _mxTable.Clear();
        _mxTable.Dispose();
    }
    #endregion
}


public class WLLLData : IDisposable
{
    private int _id;
    private string _ch;

    public int id
    {

        get { return this._id; }
        set { this._id = value; }
    }

    public string ch
    {
        get { return this._ch; }
        set { this._ch = value; }
    }
    #region IDisposable 成员
    public void Dispose()
    {
    }
    #endregion

}

public class PDAPD : IDisposable
{
    private string _chdm;
    private string _chmc;
    private string _sl;
    private string _ckid;
    private string _cw;
    private string _tm;
    private string _scddbh;
    private string _zdr;

    public string chdm
    {
        get { return this._chdm; }
        set { this._chdm = value; }
    }
    public string chmc
    {
        get { return this._chmc; }
        set { this._chmc = value; }
    }
    public string sl
    {
        get { return this._sl; }
        set { this._sl = value; }
    }
    public string ckid
    {
        get { return this._ckid; }
        set { this._ckid = value; }
    }
    public string cw
    {
        get { return this._cw; }
        set { this._cw = value; }
    }
    public string tm
    {
        get { return this._tm; }
        set { this._tm = value; }
    }
    public string scddbh
    {
        get { return this._scddbh; }
        set { this._scddbh = value; }
    }
    public string zdr
    {
        get { return this._zdr; }
        set { this._zdr = value; }
    }
    #region IDisposable 成员
    public void Dispose()
    {
    }
    #endregion
}

public class PDAJS : IDisposable
{
    private string _tm;

    public string tm
    {
        get { return this._tm; }
        set { this._tm = value; }
    }

    #region IDisposable 成员
    public void Dispose()
    {
    }
    #endregion
}

public class Result
{
    private int errcode = 0;

    public int Errcode
    {
        get { return errcode; }
        set { errcode = value; }
    }
    private string errmsg;

    public string Errmsg
    {
        get { return errmsg; }
        set { errmsg = value; }
    }
    private object data;

    public object Data
    {
        get { return data; }
        set { data = value; }
    }
}

public class TMRecode
{
    public string tm;
    public decimal sl;
    public List<int> djIDList = new List<int>();

    public void InsertID(int id)
    {
        if (!djIDList.Contains(id))
        {
            djIDList.Add(id);
        }
    }
    //保存每支布分配 情况
    public Dictionary<int, Double> tmMxid = new Dictionary<int, Double>();
}

public class DataCldjh
{
    public int djtm;
    public string djly;
}

public class DJInfo
{

    public string scddbh;
    public string chdm;
    public decimal  sl;
    public int mxid;
    public int ckid;

    public int cwid;
    public int lyscddbh;
    public int dfckid;
    public int djlx;
    public int tm;
    public decimal esl;

}
public class DJInfo2String
{

    public string scddbh;
    public string chdm;
    public decimal  sl;
    public int mxid;
    public int ckid;

    public int cwid;
    public int lyscddbh;
    public int dfckid;
    public int djlx;
    public string tm;
    public decimal esl;

}